import 'package:get/get.dart';
import 'app_routes.dart';
import '../../modules/auth/bindings/auth_binding.dart';
import '../../modules/auth/views/login_view.dart';
import '../../modules/home/<USER>/home_binding.dart';
import '../../modules/home/<USER>/home_view.dart';
import '../../modules/home/<USER>/card_editor_view.dart';
import '../../modules/home/<USER>/room_manager_view.dart';
import '../../modules/devices/bindings/devices_binding.dart';
import '../../modules/devices/views/devices_view.dart';
import '../../modules/devices/bindings/device_detail_binding.dart';
import '../../modules/devices/views/device_detail_view.dart';
import '../../modules/devices/views/device_group_manager_view.dart';
import '../../modules/scenes/bindings/scene_binding.dart';
import '../../modules/scenes/views/scenes_view.dart';
import '../../modules/scenes/views/scene_editor_view.dart';
import '../../modules/scenes/views/scene_group_manager_view.dart';
import '../../modules/profile/bindings/profile_binding.dart';
import '../../modules/profile/views/profile_view.dart';
import '../../modules/profile/views/statistics_view.dart';
import '../../modules/profile/views/settings_view.dart';
import '../../modules/profile/views/backup_view.dart';
import '../../modules/profile/views/about_view.dart';
import '../views/main_container_view.dart';

/// 应用页面路由配置
class AppPages {
  // 初始路由
  static const initial = AppRoutes.login;

  // 路由定义
  static final routes = [
    // 认证相关路由
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginView(),
      binding: AuthBinding(),
      transition: Transition.noTransition, // 使用无过渡动画，确保页面一次性完整显示
      opaque: true, // 确保页面是不透明的
    ),

    // 主容器路由
    GetPage(
      name: AppRoutes.main,
      page: () => const MainContainerView(),
      bindings: [HomeBinding(), DevicesBinding(), SceneBinding(), ProfileBinding()],
      transition: Transition.noTransition,
      opaque: true,
    ),

    // 首页路由
    GetPage(
      name: AppRoutes.home,
      page: () => const HomeView(),
      binding: HomeBinding(),
      transition: Transition.noTransition, // 使用无过渡动画，避免背景闪烁
      opaque: true, // 确保页面是不透明的
    ),

    // 卡片编辑器路由
    GetPage(
      name: AppRoutes.cardEditor,
      page: () => const CardEditorView(),
      binding: HomeBinding(),
      transition: Transition.rightToLeft,
    ),

    // 房间管理路由
    GetPage(
      name: AppRoutes.roomManager,
      page: () => const RoomManagerView(),
      binding: HomeBinding(),
      transition: Transition.rightToLeft,
    ),

    // 设备相关路由
    GetPage(
      name: AppRoutes.devices,
      page: () => const DevicesView(),
      binding: DevicesBinding(),
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: AppRoutes.deviceDetail,
      page: () => const DeviceDetailView(),
      binding: DeviceDetailBinding(),
      transition: Transition.rightToLeft,
    ),

    // 设备分组管理路由
    GetPage(
      name: AppRoutes.deviceGroups,
      page: () => const DeviceGroupManagerView(),
      binding: DevicesBinding(),
      transition: Transition.rightToLeft,
    ),

    // 场景相关路由
    GetPage(
      name: AppRoutes.scenes,
      page: () => const ScenesView(),
      binding: SceneBinding(),
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: AppRoutes.sceneEditor,
      page: () => const SceneEditorView(),
      binding: SceneBinding(),
      transition: Transition.rightToLeft,
    ),

    GetPage(
      name: AppRoutes.sceneGroups,
      page: () => const SceneGroupManagerView(),
      binding: SceneBinding(),
      transition: Transition.rightToLeft,
    ),

    // 设置相关路由
    GetPage(
      name: AppRoutes.profile,
      page: () => const ProfileView(),
      binding: ProfileBinding(),
      transition: Transition.fadeIn,
    ),

    GetPage(
      name: AppRoutes.statistics,
      page: () => const StatisticsView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),

    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),

    GetPage(
      name: '/profile/backup',
      page: () => const BackupView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),

    GetPage(
      name: '/profile/about',
      page: () => const AboutView(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),
  ];
}
