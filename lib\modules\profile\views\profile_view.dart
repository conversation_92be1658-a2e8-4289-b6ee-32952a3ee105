import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../../../app/config/app_routes.dart';
import '../../../shared/widgets/custom_bottom_nav.dart';

/// 设置页面
class ProfileView extends GetView<ProfileController> {
  const ProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 数据统计与可视化
          _buildSection(
            context,
            title: '数据统计与可视化',
            icon: Icons.bar_chart,
            children: [
              _buildSettingItem(
                context,
                title: '能源消耗统计',
                subtitle: '查看家庭能源消耗趋势',
                icon: Icons.power,
                onTap: () => Get.toNamed(AppRoutes.statistics),
              ),
              _buildSettingItem(
                context,
                title: '设备使用频率',
                subtitle: '查看设备使用热力图',
                icon: Icons.device_hub,
                onTap: () => Get.toNamed(AppRoutes.statistics),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 用户界面个性化设置
          _buildSection(
            context,
            title: '用户界面设置',
            icon: Icons.palette,
            children: [
              _buildSettingItem(
                context,
                title: '主题与外观',
                subtitle: '自定义应用主题和界面风格',
                icon: Icons.color_lens,
                onTap: () => Get.toNamed(AppRoutes.settings),
              ),
              _buildSettingItem(
                context,
                title: '首页布局',
                subtitle: '自定义首页卡片布局和样式',
                icon: Icons.dashboard_customize,
                onTap: () => Get.toNamed(AppRoutes.settings),
              ),
              _buildSettingItem(
                context,
                title: '天气设置',
                subtitle: '配置天气API和位置信息',
                icon: Icons.cloud,
                onTap: () => Get.toNamed(AppRoutes.settings),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 同步与备份
          _buildSection(
            context,
            title: '同步与备份',
            icon: Icons.sync,
            children: [
              _buildSettingItem(
                context,
                title: 'WebDAV云备份',
                subtitle: '备份和恢复应用数据',
                icon: Icons.backup,
                onTap: () => Get.toNamed('/profile/backup'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 应用信息
          _buildSection(
            context,
            title: '应用信息',
            icon: Icons.info,
            children: [
              _buildSettingItem(
                context,
                title: '关于应用',
                subtitle: '版本信息和更新日志',
                icon: Icons.info_outline,
                onTap: () => Get.toNamed('/profile/about'),
              ),
              _buildSettingItem(
                context,
                title: '退出登录',
                subtitle: '退出当前账号',
                icon: Icons.logout,
                onTap: () => controller.logout(),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 版本信息
          Center(
            child: Obx(() => Text(
              '版本: ${controller.appVersion.value ?? 'Unknown'} (${controller.appBuildNumber.value ?? ''})',
              style: Theme.of(context).textTheme.bodySmall,
            )),
          ),

          const SizedBox(height: 8),

          // Home Assistant版本
          Center(
            child: Obx(() => Text(
              'Home Assistant: ${controller.haVersion.value ?? 'Unknown'}',
              style: Theme.of(context).textTheme.bodySmall,
            )),
          ),
        ],
      ),
      // 在主容器中使用，不需要底部导航栏
      // bottomNavigationBar: const CustomBottomNav(
      //   currentIndex: 3,
      // ),
    );
  }

  /// 构建设置分区
  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分区标题
        Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 分区内容
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingItem(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 24,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }
}
