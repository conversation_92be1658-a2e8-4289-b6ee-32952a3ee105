import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/local/storage_keys.dart';
import '../../../app/data/local/storage_service.dart';
import '../../../app/controllers/theme_controller.dart';
import '../../../app/services/log_service.dart';
import '../../../app/services/auth_service.dart';

/// 卡片样式枚举
enum CardStyle {
  /// 标准样式
  standard,

  /// 紧凑样式
  compact,

  /// 圆角样式
  rounded,

  /// 扁平样式
  flat,
}

/// 首页布局枚举
enum HomeLayout {
  /// 网格布局
  grid,

  /// 列表布局
  list,

  /// 瀑布流布局
  masonry,
}

/// 设置页面控制器
class SettingsController extends GetxController {
  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();

  // 主题控制器
  final ThemeController _themeController = Get.find<ThemeController>();

  // 认证服务
  final AuthService _authService = Get.find<AuthService>();

  // 卡片样式
  final Rx<CardStyle> cardStyle = CardStyle.standard.obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 首页布局
  final Rx<HomeLayout> homeLayout = HomeLayout.grid.obs;

  // 天气API设置
  final RxString weatherApiKey = ''.obs;
  final RxString weatherLocation = ''.obs;

  @override
  void onInit() {
    super.onInit();

    // 加载设置
    _loadSettings();
  }

  /// 加载设置
  void _loadSettings() {
    try {
      // 加载卡片样式
      final savedCardStyle = _storageService.getString(StorageKeys.cardStyle);
      if (savedCardStyle != null) {
        cardStyle.value = CardStyle.values.firstWhere(
          (style) => style.toString() == savedCardStyle,
          orElse: () => CardStyle.standard,
        );
      }

      // 加载首页布局
      final savedHomeLayout = _storageService.getString(StorageKeys.homeLayout);
      if (savedHomeLayout != null) {
        homeLayout.value = HomeLayout.values.firstWhere(
          (layout) => layout.toString() == savedHomeLayout,
          orElse: () => HomeLayout.grid,
        );
      }

      // 加载天气API设置
      weatherApiKey.value = _storageService.getString(StorageKeys.weatherApiKey) ?? '';
      weatherLocation.value = _storageService.getString(StorageKeys.weatherLocation) ?? '';

      LogService.to.i('已加载设置: 卡片样式=${cardStyle.value}, 首页布局=${homeLayout.value}');
    } catch (e) {
      LogService.to.e('加载设置失败', e);
    }
  }

  /// 设置卡片样式
  Future<void> setCardStyle(CardStyle style) async {
    try {
      cardStyle.value = style;
      await _storageService.saveString(StorageKeys.cardStyle, style.toString());
      LogService.to.i('已设置卡片样式: $style');
    } catch (e) {
      LogService.to.e('设置卡片样式失败', e);
    }
  }

  /// 设置首页布局
  Future<void> setHomeLayout(HomeLayout layout) async {
    try {
      homeLayout.value = layout;
      await _storageService.saveString(StorageKeys.homeLayout, layout.toString());
      LogService.to.i('已设置首页布局: $layout');
    } catch (e) {
      LogService.to.e('设置首页布局失败', e);
    }
  }

  /// 设置天气API密钥
  Future<void> setWeatherApiKey(String apiKey) async {
    try {
      weatherApiKey.value = apiKey;
      await _storageService.saveString(StorageKeys.weatherApiKey, apiKey);
      LogService.to.i('已设置天气API密钥');
    } catch (e) {
      LogService.to.e('设置天气API密钥失败', e);
    }
  }

  /// 设置天气位置
  Future<void> setWeatherLocation(String location) async {
    try {
      weatherLocation.value = location;
      await _storageService.saveString(StorageKeys.weatherLocation, location);
      LogService.to.i('已设置天气位置: $location');
    } catch (e) {
      LogService.to.e('设置天气位置失败', e);
    }
  }

  /// 切换主题模式
  void changeThemeMode(ThemeMode mode) {
    _themeController.changeThemeMode(mode);
    LogService.to.i('已切换主题模式: $mode');
  }

  /// 获取当前主题模式
  ThemeMode get currentThemeMode => _themeController.themeMode.value;

  /// 获取卡片样式显示名称
  String getCardStyleDisplayName(CardStyle style) {
    switch (style) {
      case CardStyle.standard:
        return '标准';
      case CardStyle.compact:
        return '紧凑';
      case CardStyle.rounded:
        return '圆角';
      case CardStyle.flat:
        return '扁平';
    }
  }

  /// 获取首页布局显示名称
  String getHomeLayoutDisplayName(HomeLayout layout) {
    switch (layout) {
      case HomeLayout.grid:
        return '网格';
      case HomeLayout.list:
        return '列表';
      case HomeLayout.masonry:
        return '瀑布流';
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认退出'),
          content: const Text('确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        LogService.to.i('用户确认退出登录');

        // 显示加载指示器
        isLoading.value = true;

        // 执行退出登录
        await _authService.logout();

        LogService.to.i('退出登录成功，正在导航到登录页面');

        // 重置加载状态
        isLoading.value = false;

        // 导航到登录页面，清除所有页面历史
        Get.offAllNamed('/login', predicate: (_) => false);
      } else {
        LogService.to.i('用户取消退出登录');
      }
    } catch (e) {
      // 重置加载状态
      isLoading.value = false;

      LogService.to.e('退出登录失败', e);
      Get.snackbar(
        '退出失败',
        '退出登录失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
