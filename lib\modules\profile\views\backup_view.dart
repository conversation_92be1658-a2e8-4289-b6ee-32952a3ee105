import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../controllers/backup_controller.dart';
import '../../../app/services/webdav_service.dart';

/// 备份页面
class BackupView extends GetView<BackupController> {
  const BackupView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WebDAV云备份'),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // WebDAV配置
            _buildSection(
              context,
              title: 'WebDAV配置',
              children: [
                // 服务器地址
                _buildTextField(
                  context,
                  label: '服务器地址',
                  hint: '例如: https://dav.example.com',
                  controller: controller.serverUrlController,
                ),
                
                // 用户名
                _buildTextField(
                  context,
                  label: '用户名',
                  hint: '输入WebDAV用户名',
                  controller: controller.usernameController,
                ),
                
                // 密码
                _buildTextField(
                  context,
                  label: '密码',
                  hint: '输入WebDAV密码',
                  controller: controller.passwordController,
                  obscureText: true,
                ),
                
                // 远程路径
                _buildTextField(
                  context,
                  label: '远程路径',
                  hint: '例如: /ha_smart_home/',
                  controller: controller.remotePathController,
                ),
                
                // 错误信息
                if (controller.errorMessage.value != null)
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      controller.errorMessage.value!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                
                // 操作按钮
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      ElevatedButton.icon(
                        onPressed: controller.saveWebDavConfig,
                        icon: const Icon(Icons.save),
                        label: const Text('保存配置'),
                      ),
                      ElevatedButton.icon(
                        onPressed: controller.testConnection,
                        icon: const Icon(Icons.check_circle),
                        label: const Text('测试连接'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 备份操作
            _buildSection(
              context,
              title: '备份操作',
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      ElevatedButton.icon(
                        onPressed: controller.createBackup,
                        icon: const Icon(Icons.backup),
                        label: const Text('创建备份'),
                      ),
                      ElevatedButton.icon(
                        onPressed: controller.loadBackups,
                        icon: const Icon(Icons.refresh),
                        label: const Text('刷新列表'),
                      ),
                    ],
                  ),
                ),
                
                // 备份进度
                Obx(() {
                  if (_webdavService.isBackingUp.value) {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Text('正在创建备份...'),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: _webdavService.backupProgress.value,
                          ),
                        ],
                      ),
                    );
                  }
                  
                  if (_webdavService.isRestoring.value) {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          const Text('正在恢复备份...'),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: _webdavService.restoreProgress.value,
                          ),
                        ],
                      ),
                    );
                  }
                  
                  return const SizedBox.shrink();
                }),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 备份列表
            _buildSection(
              context,
              title: '备份列表',
              children: [
                Obx(() {
                  if (_webdavService.backups.isEmpty) {
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(
                        child: Text('暂无备份'),
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _webdavService.backups.length,
                    itemBuilder: (context, index) {
                      final backup = _webdavService.backups[index];
                      return _buildBackupItem(context, backup);
                    },
                  );
                }),
              ],
            ),
          ],
        );
      }),
    );
  }
  
  /// 获取WebDAV服务
  WebDavService get _webdavService => Get.find<WebDavService>();
  
  /// 构建设置分区
  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分区标题
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        // 分区内容
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }
  
  /// 构建文本输入框
  Widget _buildTextField(
    BuildContext context, {
    required String label,
    required String hint,
    required TextEditingController controller,
    bool obscureText = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      ),
    );
  }
  
  /// 构建备份项
  Widget _buildBackupItem(BuildContext context, WebDavBackupInfo backup) {
    // 格式化日期
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final formattedDate = dateFormat.format(backup.date);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        title: Text(backup.name),
        subtitle: Text('$formattedDate - ${backup.formattedSize}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 恢复按钮
            IconButton(
              icon: const Icon(Icons.restore),
              onPressed: () => controller.restoreBackup(backup),
              tooltip: '恢复',
            ),
            // 删除按钮
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => controller.deleteBackup(backup),
              tooltip: '删除',
            ),
          ],
        ),
      ),
    );
  }
}
