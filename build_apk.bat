@echo off
echo ===================================
echo HA Smart Home APK构建脚本
echo ===================================
echo.

echo 清理旧的构建文件...
flutter clean

echo.
echo 获取依赖...
flutter pub get

echo.
echo 开始构建发布版APK...
flutter build apk --release

echo.
if %ERRORLEVEL% == 0 (
    echo 构建成功！APK文件位于:
    echo build\app\outputs\flutter-apk\app-release.apk
    
    echo.
    echo 是否要打开输出目录？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start explorer.exe "build\app\outputs\flutter-apk\"
    )
) else (
    echo 构建失败，请检查错误信息。
)

echo.
echo 按任意键退出...
pause > nul
