import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../../../app/services/auth_service.dart';
import '../../../app/config/app_constants.dart';
import '../../../app/services/log_service.dart';
import '../../../app/config/app_routes.dart';
import 'package:get_storage/get_storage.dart';

/// 登录页面控制器
class LoginController extends GetxController {
  // 服务
  final AuthService _authService = Get.find<AuthService>();

  // 存储
  final GetStorage _storage = GetStorage();

  // 表单控制器
  final TextEditingController serverUrlController = TextEditingController();
  final TextEditingController accessTokenController = TextEditingController();

  // 表单键
  final formKey = GlobalKey<FormState>();

  // 加载状态
  final RxBool isLoading = false.obs;

  // 密码可见性
  final RxBool isPasswordVisible = false.obs;

  // 记住登录状态
  final RxBool rememberMe = true.obs;

  // 服务器连接状态
  final RxBool isServerConnected = false.obs;

  // 是否正在测试连接
  final RxBool isTestingConnection = false.obs;

  // 防抖计时器
  Timer? _debounceTimer;

  // 防抖时间（毫秒）
  static const int _debounceMilliseconds = 500;

  @override
  void onInit() {
    super.onInit();
    // 从存储中加载上次登录的服务器地址和令牌（如果有）
    _loadSavedCredentials();

    // 不再自动监听WebSocket连接状态，只在用户点击测试按钮时才测试连接
    // 这样可以避免在用户未点击测试按钮时自动连接

    // 如果已经认证，直接跳转到主容器页面
    if (_authService.isAuthenticated.value) {
      Get.offAllNamed(AppRoutes.main);
    }
  }

  @override
  void onClose() {
    // 释放控制器资源
    serverUrlController.dispose();
    accessTokenController.dispose();

    // 取消防抖计时器
    _debounceTimer?.cancel();

    super.onClose();
  }

  /// 加载保存的凭据
  void _loadSavedCredentials() {
    final savedServerUrl = _storage.read<String>(AppConstants.storageServerUrl);
    final savedAccessToken = _storage.read<String>(AppConstants.storageAccessToken);
    final savedRememberMe = _storage.read<bool>(AppConstants.storageRememberMe) ?? true;

    if (savedServerUrl != null) {
      serverUrlController.text = savedServerUrl;
    } else {
      // 设置默认服务器地址
      // serverUrlController.text = 'http://number.iepose.cn';
      serverUrlController.text = 'https://numbers.kooldns.cn';
    }

    if (savedAccessToken != null) {
      accessTokenController.text = savedAccessToken;
    } else {
      // 设置默认访问令牌
      accessTokenController.text = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiIzMGI2NGNhZGQ2Mzg0Nzk5YWJhY2EyNzRiZDA3MWJjMiIsImlhdCI6MTc0MzU4MjY3MywiZXhwIjoyMDU4OTQyNjczfQ.ujUrPTTZfbB-3x_cA7OsVErtqz-x8PM5AQBKqxhu03Y';
    }

    rememberMe.value = savedRememberMe;
  }

  /// 验证服务器URL
  String? validateServerUrl(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入Home Assistant服务器地址';
    }

    // 验证URL格式（允许IP地址和域名）
    final urlRegExp = RegExp(
      r'^(http|https)://([\w-]+\.)+[\w-]+(:\d+)?(/[\w-./?%&=]*)?$|^(http|https)://\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?(/[\w-./?%&=]*)?$',
      caseSensitive: false,
    );

    if (!urlRegExp.hasMatch(value) && !value.startsWith('http://') && !value.startsWith('https://')) {
      // 尝试添加http://前缀后再验证
      final urlWithPrefix = 'http://$value';
      if (!urlRegExp.hasMatch(urlWithPrefix)) {
        return '请输入有效的URL地址，例如: http://number.iepose.cn';
      }
    }

    return null;
  }

  /// 验证访问令牌
  String? validateAccessToken(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入访问令牌';
    }

    if (value.length < 32) {
      return '访问令牌格式不正确';
    }

    return null;
  }

  /// 测试服务器连接（带防抖功能）
  ///
  /// 只有当用户点击测试按钮时才会测试连接，并且在测试成功后立即断开连接
  void testConnection() {
    // 如果正在测试连接，不执行任何操作（由cancelConnectionTest处理）
    if (isTestingConnection.value) {
      return;
    }

    // 取消之前的防抖计时器
    _debounceTimer?.cancel();

    // 创建新的防抖计时器
    _debounceTimer = Timer(Duration(milliseconds: _debounceMilliseconds), () {
      _executeConnectionTest();
    });
  }

  /// 取消连接测试
  Future<void> cancelConnectionTest() async {
    if (!isTestingConnection.value) {
      return;
    }

    LogService.to.i('用户中断了连接测试');

    try {
      // 显示取消中的状态
      Get.snackbar(
        '正在取消',
        '正在断开连接...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 1),
      );

      // 中断连接测试
      await _authService.cancelConnectionTest();

      // 确保WebSocket已完全断开连接
      LogService.to.i('连接已断开');

      // 重置状态
      isLoading.value = false;
      isTestingConnection.value = false;
      isServerConnected.value = false;

      Get.snackbar(
        '已取消',
        '已断开服务器连接',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('取消连接测试时出错', e);
      
      // 即使出错也重置状态
      isLoading.value = false;
      isTestingConnection.value = false;
      isServerConnected.value = false;
      
      Get.snackbar(
        '错误',
        '断开连接时出错: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 执行连接测试（内部方法）
  Future<void> _executeConnectionTest() async {
    if (serverUrlController.text.isEmpty) {
      Get.snackbar(
        '错误',
        '请先输入服务器地址',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;
    isTestingConnection.value = true;

    LogService.to.i('开始测试连接到服务器: ${serverUrlController.text}');

    try {
      // 测试连接
      final result = await _authService.testConnection(serverUrlController.text);

      // 如果已经取消，则不更新状态
      if (!isTestingConnection.value) {
        return;
      }

      // 更新连接状态
      isServerConnected.value = result;

      LogService.to.i('测试连接结果: $result');

      if (result) {
        Get.snackbar(
          '成功',
          '服务器连接成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '错误',
          '无法连接到服务器',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // 如果已经取消，则不显示错误
      if (!isTestingConnection.value) {
        return;
      }

      LogService.to.e('测试连接失败', e);
      isServerConnected.value = false;
      Get.snackbar(
        '错误',
        '无法连接到服务器: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
      isTestingConnection.value = false;
    }
  }

  /// 登录
  Future<void> login() async {
    // 验证表单
    if (formKey.currentState?.validate() != true) {
      return;
    }

    isLoading.value = true;

    try {
      LogService.to.i('开始登录流程...');
      LogService.to.i('服务器地址: ${serverUrlController.text}');
      // 不要打印完整的访问令牌，只打印前10个字符
      if (accessTokenController.text.length > 10) {
        LogService.to.i('访问令牌前10个字符: ${accessTokenController.text.substring(0, 10)}...');
      } else {
        LogService.to.i('访问令牌长度不足10个字符');
      }

      // 执行登录
      final result = await _authService.login(
        serverUrlController.text,
        accessTokenController.text,
        rememberMe.value,
      );

      LogService.to.i('登录结果: $result');

      if (result) {
        // 登录成功，导航到主容器页面
        LogService.to.i('登录成功，跳转到主容器页面');

        // 先将加载状态设为false，避免登录页面的加载指示器显示在主容器页面
        isLoading.value = false;

        // 使用offAllNamed导航到主容器页面
        Get.offAllNamed(AppRoutes.main);
      } else {
        LogService.to.w('登录失败，认证未通过');
        Get.snackbar(
          '登录失败',
          '认证失败，请检查服务器地址和访问令牌',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e, stackTrace) {
      LogService.to.e('登录异常', e, stackTrace);
      Get.snackbar(
        '登录失败',
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// 切换记住登录状态
  void toggleRememberMe() {
    rememberMe.value = !rememberMe.value;
  }
}
