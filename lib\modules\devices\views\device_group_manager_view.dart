import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/models/device_group_model.dart';
import '../../../app/data/models/entity_model.dart';
import '../controllers/devices_controller.dart';

/// 设备分组管理视图
class DeviceGroupManagerView extends GetView<DevicesController> {
  const DeviceGroupManagerView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设备分组管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showGroupDialog(context),
            tooltip: '添加分组',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.deviceGroups.isEmpty) {
          return const Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.folder_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('没有设备分组，点击右上角添加'),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: controller.deviceGroups.length,
          itemBuilder: (context, index) {
            final group = controller.deviceGroups[index];
            return _buildGroupItem(context, group);
          },
        );
      }),
    );
  }

  /// 构建分组项
  Widget _buildGroupItem(BuildContext context, DeviceGroupModel group) {
    // 获取分组中的设备数量
    final deviceCount = group.entityIds.length;
    
    // 获取分组中的设备
    final devices = controller.filteredEntities
        .where((entity) => group.entityIds.contains(entity.entityId))
        .toList();
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 图标
                Container(
                  width: 48.0,
                  height: 48.0,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Icon(
                    _getGroupIcon(group.icon),
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    size: 24.0,
                  ),
                ),
                const SizedBox(width: 16.0),
                
                // 名称和设备数量
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        group.name,
                        style: Theme.of(context).textTheme.titleMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        '$deviceCount 个设备',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                
                // 操作按钮
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showGroupDialog(context, group);
                    } else if (value == 'delete') {
                      _showDeleteConfirmDialog(context, group);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20.0),
                          SizedBox(width: 8.0),
                          Text('编辑'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20.0, color: Colors.red),
                          SizedBox(width: 8.0),
                          Text('删除', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            // 设备列表
            if (devices.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: devices.take(5).map((entity) {
                    return Chip(
                      label: Text(
                        controller.deviceAliases[entity.entityId] ?? entity.friendlyName,
                        style: const TextStyle(fontSize: 12.0),
                      ),
                      avatar: Icon(
                        _getEntityIcon(entity),
                        size: 16.0,
                      ),
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 显示分组对话框
  void _showGroupDialog(BuildContext context, [DeviceGroupModel? group]) {
    final isEditing = group != null;
    final nameController = TextEditingController(text: group?.name ?? '');
    final iconController = TextEditingController(text: group?.icon ?? '');
    final selectedEntityIds = <String>[].obs;
    
    // 如果是编辑，设置已选择的设备
    if (isEditing) {
      selectedEntityIds.value = List.from(group!.entityIds);
    }
    
    Get.dialog(
      Dialog(
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: 500,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  isEditing ? '编辑分组' : '添加分组',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              
              // 表单
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    // 名称
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: '分组名称',
                        hintText: '输入分组名称',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    
                    // 图标
                    TextField(
                      controller: iconController,
                      decoration: const InputDecoration(
                        labelText: '图标 (可选)',
                        hintText: '输入图标名称',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16.0),
              
              // 设备选择标题
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  '选择设备',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              
              const SizedBox(height: 8.0),
              
              // 设备列表
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: controller.filteredEntities.length,
                  itemBuilder: (context, index) {
                    final entity = controller.filteredEntities[index];
                    return Obx(() {
                      final isSelected = selectedEntityIds.contains(entity.entityId);
                      return CheckboxListTile(
                        title: Text(
                          controller.deviceAliases[entity.entityId] ?? entity.friendlyName,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          entity.entityId,
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        value: isSelected,
                        onChanged: (value) {
                          if (value == true) {
                            selectedEntityIds.add(entity.entityId);
                          } else {
                            selectedEntityIds.remove(entity.entityId);
                          }
                        },
                        secondary: Icon(_getEntityIcon(entity)),
                      );
                    });
                  },
                ),
              ),
              
              // 按钮
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: 8.0),
                    ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        if (name.isEmpty) {
                          Get.snackbar(
                            '错误',
                            '分组名称不能为空',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }
                        
                        if (selectedEntityIds.isEmpty) {
                          Get.snackbar(
                            '错误',
                            '请至少选择一个设备',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }
                        
                        if (isEditing) {
                          controller.updateDeviceGroup(
                            group!.id,
                            name,
                            iconController.text.trim().isNotEmpty ? iconController.text.trim() : null,
                            selectedEntityIds,
                          );
                        } else {
                          controller.addDeviceGroup(
                            name,
                            iconController.text.trim().isNotEmpty ? iconController.text.trim() : null,
                            selectedEntityIds,
                          );
                        }
                        
                        Get.back();
                      },
                      child: Text(isEditing ? '保存' : '添加'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, DeviceGroupModel group) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除分组 "${group.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              controller.deleteDeviceGroup(group.id);
              Get.back();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 获取分组图标
  IconData _getGroupIcon(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return Icons.folder;
    }
    
    switch (iconName) {
      case 'light':
        return Icons.lightbulb;
      case 'switch':
        return Icons.toggle_on;
      case 'sensor':
        return Icons.sensors;
      case 'climate':
        return Icons.thermostat;
      case 'fan':
        return Icons.air;
      case 'cover':
        return Icons.blinds;
      case 'media':
        return Icons.tv;
      case 'camera':
        return Icons.camera_alt;
      case 'vacuum':
        return Icons.cleaning_services;
      case 'lock':
        return Icons.lock;
      case 'scene':
        return Icons.movie;
      case 'script':
        return Icons.code;
      case 'automation':
        return Icons.auto_fix_high;
      case 'weather':
        return Icons.wb_sunny;
      case 'home':
        return Icons.home;
      case 'bedroom':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'livingroom':
        return Icons.weekend;
      case 'bathroom':
        return Icons.bathtub;
      case 'office':
        return Icons.computer;
      default:
        return Icons.folder;
    }
  }
  
  /// 获取实体图标
  IconData _getEntityIcon(EntityModel entity) {
    switch (entity.domain) {
      case 'light':
        return Icons.lightbulb;
      case 'switch':
        return Icons.toggle_on;
      case 'sensor':
        return Icons.sensors;
      case 'binary_sensor':
        return Icons.sensors;
      case 'climate':
        return Icons.thermostat;
      case 'fan':
        return Icons.air;
      case 'cover':
        return Icons.blinds;
      case 'media_player':
        return Icons.tv;
      case 'camera':
        return Icons.camera_alt;
      case 'vacuum':
        return Icons.cleaning_services;
      case 'lock':
        return Icons.lock;
      default:
        return Icons.devices;
    }
  }
}
