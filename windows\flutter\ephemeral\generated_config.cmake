# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\software\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\project\\appProject\\ha_smart_home" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\software\\flutter\\flutter"
  "PROJECT_DIR=D:\\project\\appProject\\ha_smart_home"
  "FLUTTER_ROOT=D:\\software\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\project\\appProject\\ha_smart_home\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\project\\appProject\\ha_smart_home"
  "FLUTTER_TARGET=D:\\project\\appProject\\ha_smart_home\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\project\\appProject\\ha_smart_home\\.dart_tool\\package_config.json"
)
