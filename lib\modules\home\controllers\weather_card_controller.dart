import 'dart:async';
import 'package:get/get.dart';
import '../../../app/services/log_service.dart';
import '../../../app/services/rest_api_service.dart';
import '../../../app/services/websocket_service.dart';
import '../models/weather_forecast_model.dart';
import '../models/weather_model.dart';

/// 天气卡片控制器
class WeatherCardController extends GetxController {
  // WebSocket服务
  final WebSocketService _wsService = Get.find<WebSocketService>();
  final RestApiService _restApiService = Get.find<RestApiService>();

  // 卡片ID
  final String cardId;

  // 实体ID
  final String entityId;

  // 天气预报类型
  final WeatherForecastType? forecastType;

  // 天气信息
  final Rx<WeatherModel?> weather = Rx<WeatherModel?>(null);

  // 天气预报信息
  final Rx<WeatherForecastModel?> weatherForecast = Rx<WeatherForecastModel?>(null);

  // 天气预报订阅ID
  int? _weatherForecastSubscriptionId;

  // 天气预报订阅
  StreamSubscription? _weatherForecastSubscription;

  // 是否已订阅
  final RxBool isSubscribed = false.obs;

  // 是否正在加载
  final RxBool isLoading = true.obs;

  WeatherCardController({
    required this.cardId,
    required this.entityId,
    this.forecastType,
  });

  @override
  void onInit() {
    super.onInit();

    LogService.to.i('[$cardId] 初始化天气卡片控制器: forecastType=${forecastType?.toString() ?? "null"}');

    // 加载天气信息
    _loadWeatherInfo();

    // 如果有预报类型，则订阅天气预报
    if (forecastType != null) {
      // 先取消可能存在的订阅，然后重新订阅
      unsubscribeWeatherForecast().then((_) {
        // 确保控制器仍然有效（未被销毁）
        if (Get.isRegistered<WeatherCardController>(tag: cardId)) {
          subscribeWeatherForecast();
        } else {
          LogService.to.w('[$cardId] 控制器已被销毁，不再订阅天气预报');
        }
      });
    }
  }

  /// 加载天气信息
  Future<void> _loadWeatherInfo() async {
    try {
      // 尝试从REST API获取天气数据
      final weatherData = await _restApiService.getEntityState(entityId);

      if (weatherData != null) {
        // 使用API数据创建天气模型
        weather.value = WeatherModel.fromHaResponse(weatherData);
        LogService.to.i('[$cardId] 已从API加载天气信息: ${weather.value?.state}');
      } else {
        // 如果API请求失败，使用模拟数据
        weather.value = WeatherModel.mock();
        LogService.to.w('[$cardId] API请求失败，使用模拟天气数据');
      }
    } catch (e) {
      // 发生异常时使用模拟数据
      LogService.to.e('[$cardId] 加载天气信息失败', e);
      weather.value = WeatherModel.mock();
    }
  }

  /// 订阅天气预报
  Future<void> subscribeWeatherForecast() async {
    try {
      // 如果已经订阅，则不再重复订阅
      if (isSubscribed.value && _weatherForecastSubscriptionId != null) {
        LogService.to.i('[$cardId] 已经订阅天气预报，不再重复订阅: $_weatherForecastSubscriptionId');
        return;
      }

      isLoading.value = true;

      // 如果WebSocket未连接，则返回
      if (!_wsService.isConnected.value) {
        LogService.to.w('[$cardId] WebSocket未连接，无法订阅天气预报');
        isLoading.value = false;
        return;
      }

      // 先取消可能存在的订阅
      await _weatherForecastSubscription?.cancel();
      _weatherForecastSubscription = null;

      // 获取下一个消息ID
      _weatherForecastSubscriptionId = _wsService.getNextMessageId();

      LogService.to.i('[$cardId] 准备订阅天气预报: $entityId, ${forecastType.toString()}, ID=$_weatherForecastSubscriptionId');

      // 构建订阅消息
      final message = {
        'id': _weatherForecastSubscriptionId,
        'type': 'weather/subscribe_forecast',
        'entity_id': entityId,
        'forecast_type': forecastType == WeatherForecastType.daily ? 'daily' : 'hourly',
      };

      // 发送订阅消息
      await _wsService.sendMessage(message);

      // 订阅WebSocket消息
      _weatherForecastSubscription = _wsService.onMessage.listen((data) {
        // 确保控制器仍然有效（未被销毁）
        if (!Get.isRegistered<WeatherCardController>(tag: cardId)) {
          LogService.to.w('[$cardId] 控制器已被销毁，忽略WebSocket消息');
          _weatherForecastSubscription?.cancel();
          return;
        }

        // 检查是否是成功响应
        if (data['id'] == _weatherForecastSubscriptionId &&
            data['type'] == 'result' &&
            data['success'] == true) {
          LogService.to.i('[$cardId] 天气预报订阅成功: $_weatherForecastSubscriptionId');
          isSubscribed.value = true;
          return;
        }

        // 检查是否是天气预报事件
        if (data['type'] == 'event' &&
            data['id'] == _weatherForecastSubscriptionId &&
            data['event'] != null &&
            data['event']['type'] != null) {
          try {
            // 解析天气预报数据
            final forecast = WeatherForecastModel.fromEvent(data);

            // 更新天气预报
            weatherForecast.value = forecast;
            isLoading.value = false;

            LogService.to.i('[$cardId] 已更新天气预报: ${forecast.type.toString()}, ${forecast.forecast.length}项, ID=$_weatherForecastSubscriptionId');
          } catch (e) {
            LogService.to.e('[$cardId] 解析天气预报数据失败', e);
            isLoading.value = false;
          }
        }
      });

      LogService.to.i('[$cardId] 已订阅天气预报: $entityId, ${forecastType.toString()}, ID=$_weatherForecastSubscriptionId');
    } catch (e) {
      LogService.to.e('[$cardId] 订阅天气预报失败', e);
      isLoading.value = false;
    }
  }

  /// 取消订阅天气预报
  Future<void> unsubscribeWeatherForecast() async {
    try {
      // 如果没有订阅，则直接返回
      if (!isSubscribed.value || _weatherForecastSubscriptionId == null) {
        LogService.to.i('[$cardId] 没有活跃的天气预报订阅，无需取消');

        // 确保订阅状态一致
        _weatherForecastSubscription?.cancel();
        _weatherForecastSubscription = null;
        _weatherForecastSubscriptionId = null;
        isSubscribed.value = false;
        return;
      }

      LogService.to.i('[$cardId] 开始取消天气预报订阅: $_weatherForecastSubscriptionId');

      // 取消WebSocket消息订阅
      await _weatherForecastSubscription?.cancel();
      _weatherForecastSubscription = null;

      // 如果WebSocket已连接，发送取消订阅消息
      if (_wsService.isConnected.value) {
        final unsubscribeId = _wsService.getNextMessageId();
        final message = {
          'id': unsubscribeId,
          'type': 'unsubscribe_events',
          'subscription': _weatherForecastSubscriptionId,
        };

        await _wsService.sendMessage(message);
        LogService.to.i('[$cardId] 已发送取消订阅消息: 订阅ID=$_weatherForecastSubscriptionId, 消息ID=$unsubscribeId');
      } else {
        LogService.to.w('[$cardId] WebSocket未连接，无法发送取消订阅消息');
      }

      // 记录旧的订阅ID（用于日志）
      final oldSubscriptionId = _weatherForecastSubscriptionId;

      // 重置订阅ID和状态
      _weatherForecastSubscriptionId = null;
      isSubscribed.value = false;

      // 重置天气预报数据
      weatherForecast.value = null;
      isLoading.value = true;

      LogService.to.i('[$cardId] 已完成取消天气预报订阅: $oldSubscriptionId');
    } catch (e) {
      LogService.to.e('[$cardId] 取消订阅天气预报失败', e);

      // 确保即使出错也重置状态
      _weatherForecastSubscription = null;
      _weatherForecastSubscriptionId = null;
      isSubscribed.value = false;
      weatherForecast.value = null;
      isLoading.value = true;
    }
  }

  @override
  void onClose() {
    LogService.to.i('[$cardId] 销毁天气卡片控制器');

    // 取消天气预报订阅
    unsubscribeWeatherForecast();

    super.onClose();
  }
}
