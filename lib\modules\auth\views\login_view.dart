import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/login_controller.dart';

/// 登录页面
class LoginView extends GetView<LoginController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: controller.formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 标题和Logo
                  _buildHeader(),
                  const SizedBox(height: 40),

                  // 服务器地址输入框
                  _buildServerUrlField(),
                  const SizedBox(height: 20),

                  // 访问令牌输入框
                  _buildAccessTokenField(),
                  const SizedBox(height: 16),

                  // 记住我选项
                  _buildRememberMeOption(),
                  const SizedBox(height: 32),

                  // 登录按钮
                  _buildLoginButton(),
                  const SizedBox(height: 16),

                  // 测试连接按钮
                  _buildTestConnectionButton(),
                  const SizedBox(height: 24),

                  // 帮助文本
                  _buildHelpText(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建页面标题和Logo
  Widget _buildHeader() {
    return Column(
      children: [
        // Logo
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Get.theme.colorScheme.primary.withAlpha(25),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.home_outlined,
            size: 60,
            color: Get.theme.colorScheme.primary,
          ),
        ),

        const SizedBox(height: 24),

        // 标题
        Text(
          'Home Assistant',
          style: Get.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        // 副标题
        Text(
          '智能家居控制中心',
          style: Get.textTheme.titleMedium?.copyWith(
            color: Get.theme.colorScheme.onSurface.withAlpha(179),
          ),
        ),
      ],
    );
  }

  /// 构建服务器地址输入框
  Widget _buildServerUrlField() {
    return TextFormField(
      controller: controller.serverUrlController,
      decoration: InputDecoration(
        labelText: '服务器地址',
        hintText: 'https://homeassistant.local:8123',
        prefixIcon: const Icon(Icons.link),
        suffixIcon: IconButton(
          icon: const Icon(Icons.info_outline),
          onPressed: () {
            Get.dialog(
              AlertDialog(
                title: const Text('服务器地址说明'),
                content: const Text(
                  '请输入您的Home Assistant服务器完整地址，包括协议(http/https)和端口号。\n'
                  '例如：\n'
                  'https://homeassistant.local:8123\n'
                  'http://192.168.1.100:8123\n'
                  '或者输入内网穿透代理地址。\n'
                  '例如：http://number.iepose.cn',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('了解了'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
      keyboardType: TextInputType.url,
      textInputAction: TextInputAction.next,
      validator: controller.validateServerUrl,
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }

  /// 构建访问令牌输入框
  Widget _buildAccessTokenField() {
    return Obx(() => TextFormField(
      controller: controller.accessTokenController,
      decoration: InputDecoration(
        labelText: '访问令牌',
        hintText: '输入长期访问令牌',
        prefixIcon: const Icon(Icons.vpn_key),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 切换密码可见性按钮
            IconButton(
              icon: Icon(
                controller.isPasswordVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
              ),
              onPressed: controller.togglePasswordVisibility,
            ),
            // 帮助按钮
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () {
                Get.dialog(
                  AlertDialog(
                    title: const Text('如何获取访问令牌'),
                    content: const Text(
                      '1. 登录您的Home Assistant\n'
                      '2. 点击左侧菜单底部的个人资料\n'
                      '3. 滚动到底部的"长期访问令牌"部分\n'
                      '4. 点击"创建令牌"\n'
                      '5. 输入名称(如"智能家居App")\n'
                      '6. 复制生成的令牌(注意：令牌只显示一次)',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('了解了'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
      obscureText: !controller.isPasswordVisible.value,
      validator: controller.validateAccessToken,
      autovalidateMode: AutovalidateMode.onUserInteraction,
    ));
  }

  /// 构建记住我选项
  Widget _buildRememberMeOption() {
    return Obx(() => Row(
      children: [
        Checkbox(
          value: controller.rememberMe.value,
          onChanged: (value) => controller.toggleRememberMe(),
        ),
        const Text('记住登录状态'),
      ],
    ));
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    return Obx(() => ElevatedButton(
      onPressed: controller.isLoading.value ? null : controller.login,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: const Text('登 录'),
    ));
  }

  /// 构建测试连接按钮
  Widget _buildTestConnectionButton() {
    return Obx(() => OutlinedButton(
      onPressed: controller.isTestingConnection.value
          ? controller.cancelConnectionTest
          : controller.testConnection,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: controller.isTestingConnection.value
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('取消连接'),
                const SizedBox(width: 8),
                const SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('测试连接'),
                const SizedBox(width: 8),
                if (controller.isServerConnected.value)
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
              ],
            ),
    ));
  }

  /// 构建帮助文本
  Widget _buildHelpText() {
    return Text(
      '如需帮助，请访问 Home Assistant 官方文档',
      textAlign: TextAlign.center,
      style: Get.textTheme.bodySmall?.copyWith(
        color: Get.theme.colorScheme.onSurface.withAlpha(153),
      ),
    );
  }
}
