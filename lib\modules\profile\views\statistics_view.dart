import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/statistics_controller.dart';
import '../../../app/services/statistics_service.dart';
import '../widgets/energy_chart.dart';
import '../widgets/usage_heatmap.dart';

/// 统计页面
class StatisticsView extends GetView<StatisticsController> {
  const StatisticsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('数据统计'),
          bottom: const TabBar(
            tabs: [
              Tab(text: '能源消耗'),
              Tab(text: '使用频率'),
            ],
          ),
          actions: [
            // 刷新按钮
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: controller.loadStatisticsData,
              tooltip: '刷新数据',
            ),
          ],
        ),
        body: TabBarView(
          children: [
            // 能源消耗统计
            _buildEnergyConsumptionTab(context),
            
            // 设备使用频率
            _buildDeviceUsageTab(context),
          ],
        ),
      ),
    );
  }
  
  /// 构建能源消耗统计标签页
  Widget _buildEnergyConsumptionTab(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }
      
      if (controller.errorMessage.value != null) {
        return Center(
          child: Text(
            controller.errorMessage.value!,
            style: const TextStyle(color: Colors.red),
          ),
        );
      }
      
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 周期选择
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildPeriodButton(
                      context,
                      title: '日',
                      period: EnergyPeriod.daily,
                    ),
                    _buildPeriodButton(
                      context,
                      title: '周',
                      period: EnergyPeriod.weekly,
                    ),
                    _buildPeriodButton(
                      context,
                      title: '月',
                      period: EnergyPeriod.monthly,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 能源消耗图表
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '能源消耗趋势',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.file_download),
                            onPressed: controller.exportEnergyData,
                            tooltip: '导出数据',
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: EnergyChart(
                          data: controller.currentEnergyData,
                          period: controller.selectedEnergyPeriod.value,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 设备能耗占比
            SizedBox(
              height: 300,
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '设备能耗占比',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: controller.currentEnergyData.isEmpty
                            ? const Center(
                                child: Text('暂无数据'),
                              )
                            : _buildDeviceEnergyPieChart(context),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
  
  /// 构建设备使用频率标签页
  Widget _buildDeviceUsageTab(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }
      
      if (controller.errorMessage.value != null) {
        return Center(
          child: Text(
            controller.errorMessage.value!,
            style: const TextStyle(color: Colors.red),
          ),
        );
      }
      
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 筛选选项
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '筛选选项',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('设备类型:'),
                        const SizedBox(width: 8),
                        Expanded(
                          child: DropdownButton<String>(
                            value: controller.selectedDeviceType.value,
                            isExpanded: true,
                            items: controller.deviceTypes.map((type) {
                              return DropdownMenuItem<String>(
                                value: type,
                                child: Text(controller.getDeviceTypeDisplayName(type)),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                controller.setDeviceType(value);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 使用频率热力图
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '设备使用频率热力图',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: controller.filteredDeviceUsageData.isEmpty
                            ? const Center(
                                child: Text('暂无数据'),
                              )
                            : UsageHeatmap(
                                data: controller.filteredDeviceUsageData,
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
  
  /// 构建周期按钮
  Widget _buildPeriodButton(
    BuildContext context, {
    required String title,
    required EnergyPeriod period,
  }) {
    return Obx(() {
      final isSelected = controller.selectedEnergyPeriod.value == period;
      
      return InkWell(
        onTap: () => controller.setEnergyPeriod(period),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      );
    });
  }
  
  /// 构建设备能耗饼图
  Widget _buildDeviceEnergyPieChart(BuildContext context) {
    // 获取最新的能源数据
    final data = controller.currentEnergyData.isNotEmpty
        ? controller.currentEnergyData.last
        : null;
    
    if (data == null || data.deviceData.isEmpty) {
      return const Center(
        child: Text('暂无数据'),
      );
    }
    
    // 准备饼图数据
    final pieData = data.deviceData.entries.map((entry) {
      final color = Colors.primaries[entry.key.hashCode % Colors.primaries.length];
      return PieChartSectionData(
        value: entry.value,
        title: '${entry.value.toStringAsFixed(1)}kWh',
        color: color,
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
    
    return Row(
      children: [
        // 饼图
        Expanded(
          flex: 3,
          child: PieChart(
            PieChartData(
              sections: pieData,
              centerSpaceRadius: 40,
              sectionsSpace: 2,
            ),
          ),
        ),
        
        // 图例
        Expanded(
          flex: 2,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: data.deviceData.entries.map((entry) {
              final color = Colors.primaries[entry.key.hashCode % Colors.primaries.length];
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      color: color,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        entry.key,
                        style: const TextStyle(fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
