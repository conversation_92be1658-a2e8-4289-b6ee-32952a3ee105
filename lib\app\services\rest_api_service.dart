import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../data/local/storage_service.dart';
import 'log_service.dart';

/// REST API服务，用于发送HTTP请求
class RestApiService extends GetxService {
  // Dio实例
  late Dio _dio;

  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();

  // 单例实例
  static RestApiService get to => Get.find<RestApiService>();

  /// 初始化REST API服务
  Future<RestApiService> init() async {
    LogService.to.i('初始化REST API服务');

    // 创建Dio实例
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      contentType: 'application/json',
      responseType: ResponseType.json,
    ));

    // 添加拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 获取访问令牌
        final accessToken = _storageService.getString('access_token');
        if (accessToken != null && accessToken.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
        return handler.next(options);
      },
      onResponse: (response, handler) {
        LogService.to.d('REST API响应: ${response.statusCode}');
        return handler.next(response);
      },
      onError: (DioException e, handler) {
        LogService.to.e('REST API错误: ${e.message}', e);
        return handler.next(e);
      },
    ));

    return this;
  }

  /// 获取服务器地址
  String getServerUrl() {
    return _storageService.getString('server_url') ?? '';
  }

  /// 获取实体状态
  Future<Map<String, dynamic>?> getEntityState(String entityId) async {
    try {
      final serverUrl = getServerUrl();
      if (serverUrl.isEmpty) {
        LogService.to.e('服务器地址为空，无法获取实体状态');
        return null;
      }

      // 构建API URL
      final url = '$serverUrl/api/states/$entityId';
      LogService.to.d('获取实体状态: $url');

      // 发送请求
      final response = await _dio.get(url);

      // 检查响应
      if (response.statusCode == 200) {
        LogService.to.i('获取实体状态成功: $entityId');
        return response.data as Map<String, dynamic>;
      } else {
        LogService.to.e('获取实体状态失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      LogService.to.e('获取实体状态异常', e);
      return null;
    }
  }

  /// 调用服务
  Future<bool> callService(String domain, String service, {
    String? entityId,
    Map<String, dynamic>? serviceData,
  }) async {
    try {
      final serverUrl = getServerUrl();
      if (serverUrl.isEmpty) {
        LogService.to.e('服务器地址为空，无法调用服务');
        return false;
      }

      // 构建API URL
      final url = '$serverUrl/api/services/$domain/$service';
      LogService.to.d('调用服务: $url');

      // 构建请求数据
      final data = serviceData ?? {};
      if (entityId != null) {
        data['entity_id'] = entityId;
      }

      // 发送请求
      final response = await _dio.post(url, data: data);

      // 检查响应
      if (response.statusCode == 200 || response.statusCode == 201) {
        LogService.to.i('调用服务成功: $domain.$service');
        return true;
      } else {
        LogService.to.e('调用服务失败: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      LogService.to.e('调用服务异常', e);
      return false;
    }
  }
}
