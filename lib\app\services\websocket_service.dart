import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../config/app_constants.dart';
import 'log_service.dart';

/// WebSocket服务，负责与Home Assistant的WebSocket通信
class WebSocketService extends GetxService {
  // WebSocket连接
  WebSocketChannel? _channel;

  // 连接状态
  final RxBool isConnected = false.obs;

  // 消息ID计数器
  int _msgId = 1;

  /// 获取下一个消息ID（线程安全）
  int getNextMessageId() {
    // 在Dart中，单线程事件循环模型使得这种简单的操作通常是安全的

    // 确保消息ID始终为正数
    if (_msgId <= 0) {
      LogService.to.w('检测到无效的消息ID: $_msgId，重置为1');
      _msgId = 1;
    }

    final id = _msgId;
    _msgId++;
    LogService.to.d('生成新的WebSocket消息ID: $id');
    return id;
  }

  // 消息响应流
  final _responseController = StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get onMessage => _responseController.stream;

  // 重连尝试次数
  int _reconnectAttempts = 0;

  // 重连定时器
  Timer? _reconnectTimer;

  // 心跳定时器
  Timer? _heartbeatTimer;

  // 服务器地址
  String? _serverUrl;

  // 访问令牌
  String? _accessToken;

  // 是否在断开连接后尝试重连
  bool _shouldReconnect = true;

  /// 初始化WebSocket服务
  Future<WebSocketService> init() async {
    return this;
  }

  /// 构建WebSocket URL
  String buildWebSocketUrl(String serverUrl) {
    // 确保URL格式正确
    var normalizedUrl = serverUrl.trim();

    // 移除URL末尾的斜杠
    if (normalizedUrl.endsWith('/')) {
      normalizedUrl = normalizedUrl.substring(0, normalizedUrl.length - 1);
    }

    // 替换http为ws，https为wss
    if (normalizedUrl.startsWith('http://')) {
      normalizedUrl = normalizedUrl.replaceFirst('http://', 'ws://');
    } else if (normalizedUrl.startsWith('https://')) {
      normalizedUrl = normalizedUrl.replaceFirst('https://', 'wss://');
    } else {
      // 如果URL不以http或https开头，默认添加ws://
      normalizedUrl = 'ws://$normalizedUrl';
    }

    // 确保URL包含/api/websocket路径
    if (!normalizedUrl.endsWith('/api/websocket')) {
      normalizedUrl = '$normalizedUrl/api/websocket';
    }

    if (kDebugMode) {
      print('构建的WebSocket URL: $normalizedUrl');
    }
    return normalizedUrl;
  }

  /// 连接到Home Assistant WebSocket API
  ///
  /// [serverUrl] 服务器地址
  /// [accessToken] 访问令牌
  /// [shouldReconnect] 是否在断开连接后尝试重连，默认为true
  Future<bool> connect(String serverUrl, String accessToken, {bool shouldReconnect = true}) async {
    try {
      LogService.to.i('尝试连接到服务器: $serverUrl');

      // 存储服务器地址和访问令牌
      _serverUrl = serverUrl;
      _accessToken = accessToken;

      // 设置是否应该重连的标志
      _shouldReconnect = shouldReconnect;

      // 构建WebSocket URL
      final wsUrl = buildWebSocketUrl(serverUrl);
      LogService.to.d('WebSocket URL: $wsUrl');

      // 关闭现有连接，保持相同的重连设置
      await disconnect(shouldReconnect: shouldReconnect);

      // 创建新连接
      LogService.to.d('创建WebSocket连接...');

      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));

      // 监听消息
      _channel!.stream.listen(
        _onMessage,
        onError: (e) {
          LogService.to.e('WebSocket错误', e);
          _onError(e);
        },
        onDone: () {
          LogService.to.i('WebSocket连接关闭');
          _onDone();
        },
        cancelOnError: false,
      );

      // 重置消息ID
      _msgId = 1;
      LogService.to.i('WebSocket连接初始化成功，消息ID已重置为1');

      // 重置重连尝试次数
      _reconnectAttempts = 0;

      // 启动心跳
      _startHeartbeat();

      LogService.to.i('WebSocket连接初始化成功，是否重连: $shouldReconnect');

      return true;
    } catch (e, stackTrace) {
      LogService.to.e('WebSocket连接失败', e, stackTrace);
      _onError(e);
      return false;
    }
  }

  /// 断开WebSocket连接
  ///
  /// [shouldReconnect] 是否在断开连接后尝试重连，默认为true
  Future<void> disconnect({bool shouldReconnect = true}) async {
    // 设置是否应该重连的标志
    _shouldReconnect = shouldReconnect;

    // 停止心跳
    _stopHeartbeat();

    // 停止重连定时器
    _stopReconnectTimer();

    // 关闭连接
    if (_channel != null) {
      await _channel!.sink.close(status.goingAway);
      _channel = null;
    }

    // 更新连接状态
    isConnected.value = false;

    LogService.to.i('WebSocket连接已断开，是否重连: $shouldReconnect');
  }

  /// 发送认证消息
  Future<void> authenticate() async {
    if (_channel == null || _accessToken == null) {
      throw Exception('WebSocket未连接或访问令牌未设置');
    }

    final authMessage = {
      'type': 'auth',
      'access_token': _accessToken,
    };

    _send(authMessage);
  }

  /// 发送消息
  Future<void> sendMessage(Map<String, dynamic> message) async {
    if (_channel == null) {
      throw Exception('WebSocket未连接');
    }

    // 检查消息是否已经包含ID
    if (!message.containsKey('id')) {
      // 如果消息没有ID，添加一个新ID
      final newId = getNextMessageId();
      message = {
        ...message,
        'id': newId,
      };
      LogService.to.d('为消息添加ID: $newId, type=${message['type']}');
    } else {
      // 如果消息已经包含ID，确保内部计数器大于该ID
      final messageId = message['id'] as int;

      // 确保消息ID为正数
      if (messageId <= 0) {
        LogService.to.w('检测到无效的消息ID: $messageId，替换为新ID');
        final newId = getNextMessageId();
        message['id'] = newId;
        LogService.to.d('已替换消息ID: $newId, type=${message['type']}');
      } else if (messageId >= _msgId) {
        _msgId = messageId + 1;
        LogService.to.d('更新内部消息ID计数器: $_msgId');
      }
    }

    // 记录消息ID
    LogService.to.d('发送WebSocket消息: type=${message['type']}, id=${message['id']}');

    // 发送消息
    _send(message);

    return;
  }

  /// 内部发送方法
  void _send(dynamic message) {
    if (_channel == null) return;

    final jsonString = jsonEncode(message);
    if (kDebugMode) {
      print('发送: $jsonString');
    }

    _channel!.sink.add(jsonString);
  }

  /// 处理接收到的消息
  void _onMessage(dynamic message) {
    try {
      final jsonString = message as String;
      if (kDebugMode) {
        print('接收: $jsonString');
      }

      final data = jsonDecode(jsonString) as Map<String, dynamic>;

      // 处理认证响应
      if (data['type'] == 'auth_required') {
        // 收到认证请求，发送认证消息
        authenticate();
      } else if (data['type'] == 'auth_ok') {
        // 认证成功
        isConnected.value = true;
      } else if (data['type'] == 'auth_invalid') {
        // 认证失败
        isConnected.value = false;
        // 不尝试重连，因为认证失败
        _reconnectAttempts = AppConstants.wsMaxReconnectAttempts;
      }

      // 广播消息
      _responseController.add(data);
    } catch (e) {
      if (kDebugMode) {
        print('解析消息失败: $e');
      }
    }
  }

  /// 处理错误
  void _onError(dynamic error) {
    LogService.to.e('WebSocket错误', error);

    isConnected.value = false;

    // 尝试重连
    _scheduleReconnect();
  }

  /// 处理连接关闭
  void _onDone() {
    LogService.to.i('WebSocket连接已关闭');

    isConnected.value = false;

    // 尝试重连
    _scheduleReconnect();
  }

  /// 安排重连
  void _scheduleReconnect() {
    // 如果不应该重连，则直接返回
    if (!_shouldReconnect) {
      LogService.to.i('不尝试重连，因为设置了不重连标志');
      return;
    }

    // 停止现有的重连定时器
    _stopReconnectTimer();

    // 如果超过最大重连次数，则不再重连
    if (_reconnectAttempts >= AppConstants.wsMaxReconnectAttempts) {
      LogService.to.w('超过最大重连次数，不再重连');
      return;
    }

    // 增加重连尝试次数
    _reconnectAttempts++;

    // 计算重连延迟（指数退避）
    final delay = AppConstants.wsReconnectInterval * _reconnectAttempts;

    LogService.to.i('计划在 ${delay}ms 后重连，尝试次数: $_reconnectAttempts');

    // 安排重连
    _reconnectTimer = Timer(Duration(milliseconds: delay), _reconnect);
  }

  /// 执行重连
  Future<void> _reconnect() async {
    LogService.to.i('尝试重连...');

    if (_serverUrl != null && _accessToken != null) {
      await connect(_serverUrl!, _accessToken!);
    }
  }

  /// 停止重连定时器
  void _stopReconnectTimer() {
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      _reconnectTimer!.cancel();
      _reconnectTimer = null;
    }
  }

  /// 启动心跳
  void _startHeartbeat() {
    _stopHeartbeat();

    // 每25秒发送一次心跳
    _heartbeatTimer = Timer.periodic(
      const Duration(seconds: 25),
      (_) => _sendHeartbeat(),
    );
  }

  /// 停止心跳
  void _stopHeartbeat() {
    if (_heartbeatTimer != null && _heartbeatTimer!.isActive) {
      _heartbeatTimer!.cancel();
      _heartbeatTimer = null;
    }
  }

  /// 发送心跳消息
  void _sendHeartbeat() {
    if (isConnected.value) {
      // 发送ping消息，不指定ID，让sendMessage方法自动分配
      LogService.to.d('发送心跳消息...');
      sendMessage({'type': 'ping'});
    }
  }

  @override
  void onClose() {
    disconnect();
    _responseController.close();
    super.onClose();
  }
}
