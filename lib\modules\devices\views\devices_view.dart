import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../app/services/log_service.dart';
import '../../../shared/widgets/error_view.dart';
import '../../../shared/widgets/custom_bottom_nav.dart';
import '../../../app/config/app_routes.dart';

import '../../../shared/widgets/skeleton_loader.dart';
import '../controllers/devices_controller.dart';
import 'widgets/device_list_item.dart';

/// 设备列表视图
class DevicesView extends GetView<DevicesController> {
  const DevicesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 在页面构建完成后，标记页面已加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 如果需要刷新数据，可以在这里触发
      if (controller.isDataReady.value == false && controller.isLoading.value == false) {
        LogService.to.i('设备页面加载完成，开始获取设备数据');
        controller.fetchEntities(forceRefresh: false);
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          // 如果使用缓存数据，显示缓存标记
          if (controller.haService.isUsingCachedData.value) {
            return Row(
              children: [
                const Text('设备'),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    '离线模式',
                    style: TextStyle(fontSize: 10, color: Colors.white),
                  ),
                ),
              ],
            );
          }
          return const Text('设备');
        }),
        actions: [
          // 收藏按钮
          Obx(() => IconButton(
            icon: Icon(
              controller.showFavorites.value ? Icons.star : Icons.star_border,
              color: controller.showFavorites.value ? Theme.of(context).colorScheme.primary : null,
            ),
            onPressed: controller.toggleShowFavorites,
            tooltip: controller.showFavorites.value ? '显示全部' : '显示收藏',
          )),

          // 分组按钮
          IconButton(
            icon: const Icon(Icons.folder),
            onPressed: () => _showGroupSelector(context),
            tooltip: '分组',
          ),

          // 刷新按钮
          Obx(() => IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchEntities(forceRefresh: true),
            tooltip: controller.wsService.isConnected.value ? '刷新' : '网络已断开',
            color: controller.wsService.isConnected.value ? null : Colors.orange,
          )),

          // 更多菜单
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: '更多',
            onSelected: (value) {
              if (value == 'sort_domain') {
                controller.setSortBy('domain');
              } else if (value == 'sort_name') {
                controller.setSortBy('name');
              } else if (value == 'sort_state') {
                controller.setSortBy('state');
              } else if (value == 'sort_favorite') {
                controller.setSortBy('favorite');
              } else if (value == 'manage_groups') {
                Get.toNamed('/devices/groups');
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_domain',
                child: Row(
                  children: [
                    Icon(Icons.sort, size: 20.0),
                    SizedBox(width: 8.0),
                    Text('按类型排序'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort_name',
                child: Row(
                  children: [
                    Icon(Icons.sort_by_alpha, size: 20.0),
                    SizedBox(width: 8.0),
                    Text('按名称排序'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort_state',
                child: Row(
                  children: [
                    Icon(Icons.toggle_on, size: 20.0),
                    SizedBox(width: 8.0),
                    Text('按状态排序'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort_favorite',
                child: Row(
                  children: [
                    Icon(Icons.star, size: 20.0),
                    SizedBox(width: 8.0),
                    Text('按收藏排序'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'manage_groups',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20.0),
                    SizedBox(width: 8.0),
                    Text('管理分组'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48.0),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: '搜索设备...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: controller.clearSearch,
                      )
                    : const SizedBox.shrink()),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceVariant.withAlpha(128), // 0.5 * 255 = 128
                contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
              ),
              onChanged: controller.setSearchQuery,
            ),
          ),
        ),
      ),
      body: Obx(() {
        // 使用AnimatedSwitcher实现平滑过渡
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _buildBodyContent(),
        );
      }),
      // 在主容器中使用，不需要底部导航栏
      // bottomNavigationBar: const CustomBottomNav(
      //   currentIndex: 2,
      // ),
    );
  }

  /// 构建主体内容
  Widget _buildBodyContent() {
    if (controller.isLoading.value) {
      // 使用骨架屏加载效果，提供更好的视觉体验
      return FutureBuilder(
        // 使用Future.delayed确保骨架屏先渲染
        future: Future.delayed(const Duration(milliseconds: 300)),
        builder: (context, snapshot) {
          return Stack(
            key: const ValueKey('loading'),
            children: [
              // 骨架屏在底层
              const DeviceListSkeleton(itemCount: 8),

              // 加载指示器在顶层，半透明背景，仅在骨架屏渲染后显示
              if (snapshot.connectionState == ConnectionState.done)
                Positioned(
                  bottom: 20,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface.withAlpha(230),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(25),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '加载设备中...',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      );
    }

    if (controller.errorMessage.value != null) {
      return ErrorView(
        key: const ValueKey('error'),
        message: controller.errorMessage.value!,
        onRetry: () => controller.fetchEntities(forceRefresh: true),
      );
    }

    if (controller.domains.isEmpty) {
      return const Center(
        key: ValueKey('empty'),
        child: Text('没有找到设备'),
      );
    }

    // 显示设备列表
    return _buildDeviceList();
  }

  /// 构建设备列表
  Widget _buildDeviceList() {
    return Obx(() {
      // 如果有搜索关键字，使用列表视图
      if (controller.searchQuery.value.isNotEmpty) {
        final entities = controller.filteredEntities;

        if (entities.isEmpty) {
          return Center(
            child: Text('没有找到匹配 "${controller.searchQuery.value}" 的设备'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: entities.length,
          itemBuilder: (context, index) {
            final entity = entities[index];
            return DeviceListItem(
              entity: entity,
              onToggle: () => controller.toggleEntity(entity),
              onTap: () => Get.toNamed('/devices/detail', parameters: {
                'entityId': entity.entityId,
              }),
            );
          },
        );
      }

      // 否则使用标签视图
      return GetBuilder<DevicesController>(
        builder: (ctrl) {
          // 记录当前状态
          LogService.to.d('构建标签视图: TabController长度=${ctrl.tabController.length}, 域名数量=${ctrl.domains.length}');

          // 确保有域名数据
          if (ctrl.domains.isEmpty) {
            LogService.to.w('域名列表为空，显示加载提示');

            // 尝试重新生成域名列表
            // 延迟执行，避免在构建过程中修改状态
            Future.delayed(Duration.zero, () {
              ctrl.updateDomains();
              ctrl.update();
            });

            return const Center(
              child: Text('正在加载分类...'),
            );
          }

          // 确保TabController已初始化且长度与标签数量匹配
          if (ctrl.tabController.length != ctrl.domains.length) {
            LogService.to.w('TabController长度(${ctrl.tabController.length})与域名数量(${ctrl.domains.length})不匹配，尝试重新创建...');

            // 延迟一帧后重新创建TabController
            WidgetsBinding.instance.addPostFrameCallback((_) {
              LogService.to.i('在下一帧中重新创建TabController');
              ctrl.recreateTabController();
            });

            // 如果不匹配，返回加载指示器
            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text('正在加载分类标签 (${ctrl.domains.length})...'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => ctrl.recreateTabController(),
                    child: const Text('手动修复'),
                  ),
                ],
              ),
            );
          }

          // 构建标签视图
          try {
            // 再次检查TabController是否有效
            if (ctrl.tabController.length != ctrl.domains.length) {
              LogService.to.w('构建视图前再次检查: TabController长度(${ctrl.tabController.length})与域名数量(${ctrl.domains.length})不匹配');

              // 尝试立即重新创建TabController
              WidgetsBinding.instance.addPostFrameCallback((_) {
                LogService.to.i('尝试立即重新创建TabController');
                // 使用延迟执行，确保在下一帧重新构建UI
                Future.delayed(Duration.zero, () {
                  ctrl.update();
                });
              });

              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text('正在准备分类标签 (${ctrl.domains.length})...'),
                  ],
                ),
              );
            }

            // TabController有效，构建标签视图
            return Column(
              children: [
                TabBar(
                  controller: ctrl.tabController,
                  isScrollable: true,
                  tabs: ctrl.domains.map((domain) {
                    return Tab(
                      text: _getDomainDisplayName(domain),
                    );
                  }).toList(),
                  onTap: (index) {
                    if (index < ctrl.domains.length) {
                      ctrl.setSelectedDomain(ctrl.domains[index]);
                    }
                  },
                ),
                Expanded(
                  child: TabBarView(
                    controller: ctrl.tabController,
                    children: ctrl.domains.map((domain) {
                      return _buildDomainTab(domain);
                    }).toList(),
                  ),
                ),
              ],
            );
          } catch (e, stackTrace) {
            // 如果构建标签视图时出错，显示错误信息
            LogService.to.e('构建标签视图失败', e, stackTrace);

            // 尝试立即重新创建TabController
            WidgetsBinding.instance.addPostFrameCallback((_) {
              LogService.to.i('发生错误，尝试重新创建TabController');
              // 使用延迟执行，确保在下一帧重新构建UI
              Future.delayed(Duration.zero, () {
                ctrl.update();
              });
            });

            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('加载分类标签失败: $e'),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => ctrl.fetchEntities(forceRefresh: true),
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }
        },
      );
    });
  }

  /// 构建域名标签页
  Widget _buildDomainTab(String domain) {
    return Obx(() {
      List<EntityModel> entities;

      if (domain == 'all') {
        entities = controller.filteredEntities;
      } else {
        entities = controller.entitiesByDomain[domain] ?? [];
      }

      if (entities.isEmpty) {
        return const Center(
          child: Text('没有设备'),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: entities.length,
        itemBuilder: (context, index) {
          final entity = entities[index];
          return DeviceListItem(
            entity: entity,
            onToggle: () => controller.toggleEntity(entity),
            onTap: () => Get.toNamed('/devices/detail', parameters: {
              'entityId': entity.entityId,
            }),
            onFavoriteToggle: () => controller.toggleFavorite(entity.entityId),
            onSetAlias: () => _showAliasDialog(context, entity),
            isFavorite: controller.favoriteDevices.contains(entity.entityId),
            alias: controller.deviceAliases[entity.entityId],
          );
        },
      );
    });
  }

  /// 显示分组选择器对话框
  void _showGroupSelector(BuildContext context) {
    Get.dialog(
      Dialog(
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
            maxWidth: 400,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  '选择设备分组',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),

              // 分组列表
              Expanded(
                child: Obx(() {
                  final groups = controller.deviceGroups;

                  if (groups.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.folder_outlined, size: 48, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('没有设备分组'),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    itemCount: groups.length + 1, // +1 for "All" option
                    itemBuilder: (context, index) {
                      // 全部选项
                      if (index == 0) {
                        return ListTile(
                          leading: const Icon(Icons.all_inclusive),
                          title: const Text('全部设备'),
                          selected: controller.selectedGroupId.value == 'all',
                          onTap: () {
                            controller.setSelectedGroup('all');
                            Get.back();
                          },
                        );
                      }

                      // 分组选项
                      final group = groups[index - 1];
                      return ListTile(
                        leading: Icon(_getGroupIcon(group.icon)),
                        title: Text(group.name),
                        subtitle: Text('${group.entityIds.length} 个设备'),
                        selected: controller.selectedGroupId.value == group.id,
                        onTap: () {
                          controller.setSelectedGroup(group.id);
                          Get.back();
                        },
                      );
                    },
                  );
                }),
              ),

              // 底部按钮
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: 8.0),
                    ElevatedButton(
                      onPressed: () => Get.toNamed('/devices/groups'),
                      child: const Text('管理分组'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取分组图标
  IconData _getGroupIcon(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return Icons.folder;
    }

    switch (iconName) {
      case 'light':
        return Icons.lightbulb;
      case 'switch':
        return Icons.toggle_on;
      case 'sensor':
        return Icons.sensors;
      case 'climate':
        return Icons.thermostat;
      case 'fan':
        return Icons.air;
      case 'cover':
        return Icons.blinds;
      case 'media':
        return Icons.tv;
      case 'camera':
        return Icons.camera_alt;
      case 'vacuum':
        return Icons.cleaning_services;
      case 'lock':
        return Icons.lock;
      case 'scene':
        return Icons.movie;
      case 'script':
        return Icons.code;
      case 'automation':
        return Icons.auto_fix_high;
      case 'weather':
        return Icons.wb_sunny;
      case 'home':
        return Icons.home;
      case 'bedroom':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'livingroom':
        return Icons.weekend;
      case 'bathroom':
        return Icons.bathtub;
      case 'office':
        return Icons.computer;
      default:
        return Icons.folder;
    }
  }

  /// 显示设置别名对话框
  void _showAliasDialog(BuildContext context, EntityModel entity) {
    final TextEditingController aliasController = TextEditingController();

    // 设置初始值
    final currentAlias = controller.deviceAliases[entity.entityId];
    if (currentAlias != null) {
      aliasController.text = currentAlias;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('设置设备别名'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('设备: ${entity.friendlyName}'),
            const SizedBox(height: 16),
            TextField(
              controller: aliasController,
              decoration: const InputDecoration(
                labelText: '别名',
                hintText: '输入设备别名',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              controller.setDeviceAlias(entity.entityId, aliasController.text.trim());
              Get.back();
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 获取域名显示名称
  String _getDomainDisplayName(String domain) {
    switch (domain) {
      case 'all':
        return '全部';
      case 'light':
        return '灯光';
      case 'switch':
        return '开关';
      case 'sensor':
        return '传感器';
      case 'binary_sensor':
        return '二进制传感器';
      case 'climate':
        return '空调';
      case 'fan':
        return '风扇';
      case 'cover':
        return '窗帘';
      case 'media_player':
        return '媒体播放器';
      case 'camera':
        return '摄像头';
      case 'vacuum':
        return '扫地机器人';
      case 'lock':
        return '门锁';
      case 'scene':
        return '场景';
      case 'script':
        return '脚本';
      case 'automation':
        return '自动化';
      case 'weather':
        return '天气';
      case 'sun':
        return '太阳';
      case 'device_tracker':
        return '设备追踪';
      case 'person':
        return '人员';
      case 'zone':
        return '区域';
      default:
        return domain;
    }
  }
}
