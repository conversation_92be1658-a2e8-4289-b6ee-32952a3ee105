/// 应用路由名称定义
abstract class AppRoutes {
  // 认证相关路由
  static const login = '/login';

  // 主容器路由
  static const main = '/main';

  // 主页相关路由
  static const home = '/home';
  static const cardEditor = '/home/<USER>';
  static const roomManager = '/home/<USER>';

  // 场景相关路由
  static const scenes = '/scenes';
  static const sceneEditor = '/scenes/editor';
  static const sceneGroups = '/scenes/groups';

  // 设备相关路由
  static const devices = '/devices';
  static const deviceDetail = '/devices/detail';
  static const deviceGroups = '/devices/groups';

  // 设置相关路由
  static const profile = '/profile';
  static const settings = '/profile/settings';
  static const statistics = '/profile/statistics';
}
