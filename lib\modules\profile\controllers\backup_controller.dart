import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/services/webdav_service.dart';
import '../../../app/services/log_service.dart';

/// 备份页面控制器
class BackupController extends GetxController {
  // WebDAV服务
  final WebDavService _webdavService = Get.find<WebDavService>();
  
  // 表单控制器
  final TextEditingController serverUrlController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController remotePathController = TextEditingController();
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  @override
  void onInit() {
    super.onInit();
    
    // 加载WebDAV配置
    _loadWebDavConfig();
    
    // 加载备份列表
    loadBackups();
  }
  
  @override
  void onClose() {
    // 释放控制器
    serverUrlController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    remotePathController.dispose();
    
    super.onClose();
  }
  
  /// 加载WebDAV配置
  void _loadWebDavConfig() {
    try {
      serverUrlController.text = _webdavService.serverUrl.value;
      usernameController.text = _webdavService.username.value;
      passwordController.text = _webdavService.password.value;
      remotePathController.text = _webdavService.remotePath.value;
      
      LogService.to.i('已加载WebDAV配置: ${_webdavService.serverUrl.value}');
    } catch (e) {
      LogService.to.e('加载WebDAV配置失败', e);
    }
  }
  
  /// 保存WebDAV配置
  Future<bool> saveWebDavConfig() async {
    try {
      isLoading.value = true;
      errorMessage.value = null;
      
      // 验证输入
      if (serverUrlController.text.isEmpty) {
        errorMessage.value = '请输入服务器地址';
        isLoading.value = false;
        return false;
      }
      
      if (usernameController.text.isEmpty) {
        errorMessage.value = '请输入用户名';
        isLoading.value = false;
        return false;
      }
      
      if (passwordController.text.isEmpty) {
        errorMessage.value = '请输入密码';
        isLoading.value = false;
        return false;
      }
      
      // 保存配置
      await _webdavService.saveConfig(
        serverUrl: serverUrlController.text,
        username: usernameController.text,
        password: passwordController.text,
        remotePath: remotePathController.text.isEmpty ? '/ha_smart_home/' : remotePathController.text,
      );
      
      isLoading.value = false;
      return true;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = '保存WebDAV配置失败: $e';
      LogService.to.e('保存WebDAV配置失败', e);
      return false;
    }
  }
  
  /// 测试WebDAV连接
  Future<bool> testConnection() async {
    try {
      isLoading.value = true;
      errorMessage.value = null;
      
      // 保存配置
      final saved = await saveWebDavConfig();
      if (!saved) {
        return false;
      }
      
      // 测试连接
      final connected = await _webdavService.connect();
      
      isLoading.value = false;
      
      if (connected) {
        Get.snackbar(
          '连接成功',
          '已成功连接到WebDAV服务器',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        errorMessage.value = _webdavService.connectionError.value;
        Get.snackbar(
          '连接失败',
          '无法连接到WebDAV服务器: ${_webdavService.connectionError.value}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
      
      return connected;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = '测试WebDAV连接失败: $e';
      LogService.to.e('测试WebDAV连接失败', e);
      
      Get.snackbar(
        '连接失败',
        '测试WebDAV连接失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      
      return false;
    }
  }
  
  /// 加载备份列表
  Future<void> loadBackups() async {
    try {
      isLoading.value = true;
      errorMessage.value = null;
      
      // 检查是否已配置
      if (_webdavService.serverUrl.isEmpty || _webdavService.username.isEmpty || _webdavService.password.isEmpty) {
        isLoading.value = false;
        return;
      }
      
      // 获取备份列表
      await _webdavService.getBackups();
      
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = '加载备份列表失败: $e';
      LogService.to.e('加载备份列表失败', e);
    }
  }
  
  /// 创建备份
  Future<void> createBackup() async {
    try {
      // 检查是否已连接
      if (!_webdavService.isConnected.value) {
        final connected = await testConnection();
        if (!connected) {
          return;
        }
      }
      
      // 创建备份
      final success = await _webdavService.createBackup();
      
      if (success) {
        Get.snackbar(
          '备份成功',
          '已成功创建备份',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '备份失败',
          '创建备份失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LogService.to.e('创建备份失败', e);
      Get.snackbar(
        '备份失败',
        '创建备份失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 恢复备份
  Future<void> restoreBackup(WebDavBackupInfo backup) async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认恢复'),
          content: Text('确定要恢复备份 ${backup.name} 吗？这将覆盖当前的所有数据。'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );
      
      if (confirmed != true) {
        return;
      }
      
      // 恢复备份
      final success = await _webdavService.restoreBackup(backup);
      
      if (success) {
        Get.snackbar(
          '恢复成功',
          '已成功恢复备份',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        // 提示重启应用
        await Get.dialog(
          AlertDialog(
            title: const Text('恢复完成'),
            content: const Text('备份已恢复，建议重启应用以应用所有更改。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('确定'),
              ),
            ],
          ),
        );
      } else {
        Get.snackbar(
          '恢复失败',
          '恢复备份失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LogService.to.e('恢复备份失败', e);
      Get.snackbar(
        '恢复失败',
        '恢复备份失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 删除备份
  Future<void> deleteBackup(WebDavBackupInfo backup) async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除备份 ${backup.name} 吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );
      
      if (confirmed != true) {
        return;
      }
      
      // 删除备份
      final success = await _webdavService.deleteBackup(backup);
      
      if (success) {
        Get.snackbar(
          '删除成功',
          '已成功删除备份',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '删除失败',
          '删除备份失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LogService.to.e('删除备份失败', e);
      Get.snackbar(
        '删除失败',
        '删除备份失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
