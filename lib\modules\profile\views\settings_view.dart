import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/settings_controller.dart';

/// 设置页面
class SettingsView extends GetView<SettingsController> {
  const SettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('界面设置'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 主题设置
          _buildSection(
            context,
            title: '主题设置',
            children: [
              // 主题模式
              _buildSettingItem(
                context,
                title: '主题模式',
                subtitle: '选择应用的主题模式',
                trailing: Obx(() => DropdownButton<ThemeMode>(
                  value: controller.currentThemeMode,
                  underline: const SizedBox(),
                  items: const [
                    DropdownMenuItem(
                      value: ThemeMode.system,
                      child: Text('跟随系统'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.light,
                      child: Text('亮色模式'),
                    ),
                    DropdownMenuItem(
                      value: ThemeMode.dark,
                      child: Text('暗色模式'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      controller.changeThemeMode(value);
                    }
                  },
                )),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 卡片样式设置
          _buildSection(
            context,
            title: '卡片样式设置',
            children: [
              // 卡片样式
              _buildSettingItem(
                context,
                title: '卡片样式',
                subtitle: '选择首页卡片的显示样式',
                trailing: Obx(() => DropdownButton<CardStyle>(
                  value: controller.cardStyle.value,
                  underline: const SizedBox(),
                  items: CardStyle.values.map((style) {
                    return DropdownMenuItem(
                      value: style,
                      child: Text(controller.getCardStyleDisplayName(style)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.setCardStyle(value);
                    }
                  },
                )),
              ),

              // 首页布局
              _buildSettingItem(
                context,
                title: '首页布局',
                subtitle: '选择首页卡片的布局方式',
                trailing: Obx(() => DropdownButton<HomeLayout>(
                  value: controller.homeLayout.value,
                  underline: const SizedBox(),
                  items: HomeLayout.values.map((layout) {
                    return DropdownMenuItem(
                      value: layout,
                      child: Text(controller.getHomeLayoutDisplayName(layout)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.setHomeLayout(value);
                    }
                  },
                )),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 天气设置
          _buildSection(
            context,
            title: '天气设置',
            children: [
              // 天气API密钥
              _buildTextFieldItem(
                context,
                title: '天气API密钥',
                subtitle: '输入和风天气API密钥',
                initialValue: controller.weatherApiKey.value,
                onChanged: (value) {
                  controller.setWeatherApiKey(value);
                },
              ),

              // 天气位置
              _buildTextFieldItem(
                context,
                title: '天气位置',
                subtitle: '输入城市名称或坐标',
                initialValue: controller.weatherLocation.value,
                onChanged: (value) {
                  controller.setWeatherLocation(value);
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 账户设置
          _buildSection(
            context,
            title: '账户设置',
            children: [
              // 退出登录按钮
              Obx(() => _buildActionItem(
                context,
                title: '退出登录',
                subtitle: '退出当前账号并返回登录页面',
                icon: Icons.logout,
                iconColor: Colors.red,
                isLoading: controller.isLoading.value,
                onTap: controller.logout,
              )),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建设置分区
  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分区标题
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 8),
        // 分区内容
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 约等于 0.05 的不透明度
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingItem(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
          trailing,
        ],
      ),
    );
  }

  /// 构建文本输入设置项
  Widget _buildTextFieldItem(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String initialValue,
    required Function(String) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: TextEditingController(text: initialValue),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮设置项
  Widget _buildActionItem(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Function() onTap,
    Color? iconColor,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: iconColor ?? Theme.of(context).colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            if (isLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
            else
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
          ],
        ),
      ),
    );
  }
}
