import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../../app/services/auth_service.dart';
import '../../../app/services/log_service.dart';
import '../../../app/config/app_constants.dart';

/// 设置页面主控制器
class ProfileController extends GetxController {
  // 服务
  final AuthService _authService = Get.find<AuthService>();

  // 应用信息
  final Rx<String?> appVersion = Rx<String?>(null);
  final Rx<String?> appBuildNumber = Rx<String?>(null);

  // Home Assistant版本
  final Rx<String?> haVersion = Rx<String?>(null);

  // 加载状态
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    // 加载应用信息
    _loadAppInfo();

    // 获取Home Assistant版本
    haVersion.value = _authService.haVersion.value;
  }

  /// 加载应用信息
  Future<void> _loadAppInfo() async {
    try {
      isLoading.value = true;

      // 获取应用信息
      final packageInfo = await PackageInfo.fromPlatform();

      appVersion.value = packageInfo.version;
      appBuildNumber.value = packageInfo.buildNumber;

      LogService.to.i('应用版本: ${appVersion.value}, 构建号: ${appBuildNumber.value}');

      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      LogService.to.e('加载应用信息失败', e);

      // 使用默认值
      appVersion.value = AppConstants.appVersion;
      appBuildNumber.value = '1';
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认退出'),
          content: const Text('确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        LogService.to.i('用户确认退出登录');

        // 显示加载指示器
        isLoading.value = true;

        // 执行退出登录
        await _authService.logout();

        LogService.to.i('退出登录成功，正在导航到登录页面');

        // 重置加载状态
        isLoading.value = false;

        // 导航到登录页面，清除所有页面历史
        Get.offAllNamed('/login', predicate: (_) => false);
      } else {
        LogService.to.i('用户取消退出登录');
      }
    } catch (e) {
      // 重置加载状态
      isLoading.value = false;

      LogService.to.e('退出登录失败', e);
      Get.snackbar(
        '退出失败',
        '退出登录失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
