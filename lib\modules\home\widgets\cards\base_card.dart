import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 卡片样式枚举
enum CardStyle {
  /// 默认样式
  defaultStyle,

  /// 拟物风格
  neumorphic,

  /// 毛玻璃风格
  glassmorphism,

  /// 极简风格
  minimal,
}

/// 卡片尺寸枚举
enum CardSize {
  /// 小尺寸 (1x1)
  small,

  /// 中尺寸 (2x1)
  medium,

  /// 大尺寸 (2x2)
  large,
}

/// 基础卡片组件
/// 所有设备卡片都应该继承自这个组件
class BaseCard extends StatelessWidget {
  /// 卡片ID
  final String id;

  /// 卡片标题
  final String title;

  /// 自定义标题（如果提供，将覆盖默认标题）
  final String? customTitle;

  /// 卡片图标
  final IconData icon;

  /// 卡片内容
  final Widget content;

  /// 卡片样式
  final CardStyle style;

  /// 卡片尺寸
  final CardSize size;

  /// 是否可编辑
  final bool editable;

  /// 是否处于编辑模式
  final bool editMode;

  /// 是否显示标题栏
  final bool showTitle;

  /// 点击事件
  final VoidCallback? onTap;

  /// 长按事件
  final VoidCallback? onLongPress;

  /// 删除事件
  final VoidCallback? onDelete;

  /// 编辑事件
  final VoidCallback? onEdit;

  const BaseCard({
    super.key,
    required this.id,
    required this.title,
    this.customTitle,
    required this.icon,
    required this.content,
    this.style = CardStyle.defaultStyle,
    this.size = CardSize.medium,
    this.editable = true,
    this.editMode = false,
    this.showTitle = true,
    this.onTap,
    this.onLongPress,
    this.onDelete,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    // 根据尺寸确定卡片宽高比
    final aspectRatio = _getAspectRatio();

    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Card(
        elevation: _getElevation(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.all(8),
        child: _buildCardContent(context),
      ),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent(BuildContext context) {
    // 根据样式应用不同的装饰
    Widget cardContent = Container(
      decoration: _getDecoration(context),
      child: Stack(
        children: [
          // 卡片内容
          InkWell(
            onTap: editMode ? null : onTap,
            onLongPress: editMode ? null : onLongPress,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 卡片标题栏（如果显示）
                  if (showTitle) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 标题和图标
                        Row(
                          children: [
                            Icon(
                              icon,
                              size: 20,
                              color: _getTitleColor(context),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              // 如果有自定义标题，则使用自定义标题，否则使用默认标题
                              customTitle ?? title,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: _getTitleColor(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    // 分隔线
                    Divider(
                      height: 16,
                      thickness: 1,
                      color: _getDividerColor(context),
                    ),
                  ],

                  // 卡片主体内容
                  Expanded(
                    child: content,
                  ),
                ],
              ),
            ),
          ),
          
          // 移除编辑模式下的操作按钮，只保持卡片被选中的视觉效果
          if (editMode && editable)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.blue,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
        ],
      ),
    );

    // 应用毛玻璃效果
    if (style == CardStyle.glassmorphism) {
      cardContent = ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: _getBlurFilter(),
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }

  /// 获取卡片宽高比
  double _getAspectRatio() {
    switch (size) {
      case CardSize.small:
        return 1.0; // 1:1
      case CardSize.medium:
        return 2.0; // 2:1
      case CardSize.large:
        return 1.0; // 2:2 (显示为1:1，但占用2x2网格)
    }
  }

  /// 获取卡片阴影
  double _getElevation() {
    switch (style) {
      case CardStyle.defaultStyle:
        return 2.0;
      case CardStyle.neumorphic:
        return 4.0;
      case CardStyle.glassmorphism:
        return 0.0;
      case CardStyle.minimal:
        return 0.0;
    }
  }

  /// 获取卡片装饰
  Decoration _getDecoration(BuildContext context) {
    switch (style) {
      case CardStyle.defaultStyle:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
        );
      case CardStyle.neumorphic:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 10,
              offset: const Offset(5, 5),
            ),
            BoxShadow(
              color: Colors.white.withAlpha(26),
              blurRadius: 10,
              offset: const Offset(-5, -5),
            ),
          ],
        );
      case CardStyle.glassmorphism:
        return BoxDecoration(
          color: Theme.of(context).cardColor.withAlpha(179),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withAlpha(51),
            width: 1.5,
          ),
        );
      case CardStyle.minimal:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        );
    }
  }

  /// 获取模糊滤镜
  dynamic _getBlurFilter() {
    // 在实际应用中，这里应该返回一个ImageFilter.blur
    // 但由于简化，这里返回null
    return null;
  }

  /// 获取标题颜色
  Color _getTitleColor(BuildContext context) {
    return Theme.of(context).textTheme.titleMedium?.color ?? Colors.black;
  }

  /// 获取分隔线颜色
  Color _getDividerColor(BuildContext context) {
    return Theme.of(context).dividerColor;
  }
}
