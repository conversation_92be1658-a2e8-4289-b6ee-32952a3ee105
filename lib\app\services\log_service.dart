import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

/// 日志级别
enum LogLevel {
  verbose,
  debug,
  info,
  warning,
  error,
  wtf,
  nothing,
}

/// 日志服务，提供统一的日志记录功能
class LogService extends GetxService {
  // 日志实例
  late Logger _logger;
  
  // 是否启用日志
  final RxBool isEnabled = true.obs;
  
  // 日志级别
  final Rx<LogLevel> logLevel = LogLevel.debug.obs;
  
  // 单例实例
  static LogService get to => Get.find<LogService>();
  
  /// 初始化日志服务
  Future<LogService> init() async {
    // 创建日志实例
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.verbose : Level.warning,
      filter: ProductionFilter(),
    );
    
    return this;
  }
  
  /// 记录详细日志
  void v(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.verbose)) {
      _logger.v(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 记录调试日志
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.debug)) {
      _logger.d(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 记录信息日志
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.info)) {
      _logger.i(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 记录警告日志
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.warning)) {
      _logger.w(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 记录错误日志
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.error)) {
      _logger.e(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 记录严重错误日志
  void wtf(String message, [dynamic error, StackTrace? stackTrace]) {
    if (isEnabled.value && _shouldLog(LogLevel.wtf)) {
      _logger.wtf(message, error: error, stackTrace: stackTrace);
    }
  }
  
  /// 判断是否应该记录指定级别的日志
  bool _shouldLog(LogLevel level) {
    return level.index >= logLevel.value.index;
  }
  
  /// 设置日志级别
  void setLogLevel(LogLevel level) {
    logLevel.value = level;
  }
  
  /// 启用日志
  void enable() {
    isEnabled.value = true;
  }
  
  /// 禁用日志
  void disable() {
    isEnabled.value = false;
  }
}
