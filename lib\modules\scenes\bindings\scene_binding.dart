import 'package:get/get.dart';
import '../controllers/scenes_controller.dart';
import '../controllers/scene_editor_controller.dart';
import '../../../app/data/repositories/scene_repository.dart';

/// 场景模块依赖注入
class SceneBinding extends Bindings {
  @override
  void dependencies() {
    // 注入场景仓库
    Get.lazyPut<SceneRepository>(() => SceneRepository());
    
    // 注入场景列表控制器
    Get.lazyPut<ScenesController>(() => ScenesController());
    
    // 注入场景编辑控制器
    Get.lazyPut<SceneEditorController>(() => SceneEditorController());
  }
}
