import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 场景模型
class SceneModel {
  final String id;
  final String name;
  final String? icon;
  final Color color;
  final String? groupId;
  final List<String> entityIds;
  final int order;
  final bool isCustom; // 是否是自定义场景（非HA内置场景）
  
  SceneModel({
    required this.id,
    required this.name,
    this.icon,
    required this.color,
    this.groupId,
    required this.entityIds,
    this.order = 0,
    this.isCustom = true,
  });
  
  /// 从JSON创建
  factory SceneModel.fromJson(Map<String, dynamic> json) {
    return SceneModel(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String?,
      color: Color(json['color'] as int),
      groupId: json['group_id'] as String?,
      entityIds: (json['entity_ids'] as List<dynamic>).cast<String>(),
      order: json['order'] as int? ?? 0,
      isCustom: json['is_custom'] as bool? ?? true,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color.value,
      'group_id': groupId,
      'entity_ids': entityIds,
      'order': order,
      'is_custom': isCustom,
    };
  }
  
  /// 创建副本
  SceneModel copyWith({
    String? id,
    String? name,
    String? icon,
    Color? color,
    String? groupId,
    List<String>? entityIds,
    int? order,
    bool? isCustom,
  }) {
    return SceneModel(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      groupId: groupId ?? this.groupId,
      entityIds: entityIds ?? this.entityIds,
      order: order ?? this.order,
      isCustom: isCustom ?? this.isCustom,
    );
  }
  
  @override
  String toString() {
    return 'SceneModel{id: $id, name: $name, entityIds: ${entityIds.length}}';
  }
}
