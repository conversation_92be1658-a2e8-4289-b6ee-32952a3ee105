import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../../app/data/local/storage_service.dart';
import '../../../app/services/ha_service.dart';
import '../../../app/services/log_service.dart';
import '../models/weather_forecast_model.dart';
import '../widgets/cards/base_card.dart';

/// 卡片类型枚举
enum CardType {
  /// 天气卡片
  weather,

  /// 灯光卡片
  light,

  /// 温控卡片
  climate,

  /// 摄像头卡片
  camera,

  /// 传感器卡片
  sensor,

  /// 窗帘卡片
  cover,

  /// 门锁卡片
  lock,

  /// 媒体卡片
  media,

  /// 净化器卡片
  purifier,

  /// 扫地机卡片
  vacuum,

  /// 能源卡片
  energy,
}

/// 卡片模型
class CardModel {
  /// 卡片ID
  final String id;

  /// 卡片类型
  final CardType type;

  /// 实体ID
  final String? entityId;

  /// 卡片样式
  final CardStyle style;

  /// 卡片尺寸
  final CardSize size;

  /// 卡片位置
  final int position;

  /// 房间ID
  final String? roomId;

  /// 天气预报类型（仅天气卡片）
  final WeatherForecastType? forecastType;

  /// 自定义标题
  final String? customTitle;

  /// 是否显示标题栏
  final bool showTitle;

  CardModel({
    required this.id,
    required this.type,
    this.entityId,
    this.style = CardStyle.defaultStyle,
    this.size = CardSize.medium,
    required this.position,
    this.roomId,
    this.forecastType,
    this.customTitle,
    this.showTitle = true,
  });

  /// 从JSON创建
  factory CardModel.fromJson(Map<String, dynamic> json) {
    // 解析天气预报类型
    WeatherForecastType? forecastType;
    if (json['forecast_type'] != null) {
      final forecastTypeStr = json['forecast_type'] as String;
      forecastType = forecastTypeStr == 'daily'
          ? WeatherForecastType.daily
          : forecastTypeStr == 'hourly'
              ? WeatherForecastType.hourly
              : null;
    }

    return CardModel(
      id: json['id'] as String,
      type: CardType.values.firstWhere(
        (e) => e.toString() == 'CardType.${json['type']}',
        orElse: () => CardType.sensor,
      ),
      entityId: json['entity_id'] as String?,
      style: CardStyle.values.firstWhere(
        (e) => e.toString() == 'CardStyle.${json['style']}',
        orElse: () => CardStyle.defaultStyle,
      ),
      size: CardSize.values.firstWhere(
        (e) => e.toString() == 'CardSize.${json['size']}',
        orElse: () => CardSize.medium,
      ),
      position: json['position'] as int,
      roomId: json['room_id'] as String?,
      forecastType: forecastType,
      customTitle: json['custom_title'] as String?,
      showTitle: json['show_title'] as bool? ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final json = {
      'id': id,
      'type': type.toString().split('.').last,
      'entity_id': entityId,
      'style': style.toString().split('.').last,
      'size': size.toString().split('.').last,
      'position': position,
      'room_id': roomId,
      'custom_title': customTitle,
      'show_title': showTitle,
    };

    // 添加天气预报类型
    if (forecastType != null) {
      json['forecast_type'] = forecastType.toString().split('.').last;
    }

    return json;
  }

  /// 创建副本
  CardModel copyWith({
    String? id,
    CardType? type,
    String? entityId,
    CardStyle? style,
    CardSize? size,
    int? position,
    String? roomId,
    WeatherForecastType? forecastType,
    String? customTitle,
    bool? showTitle,
  }) {
    return CardModel(
      id: id ?? this.id,
      type: type ?? this.type,
      entityId: entityId ?? this.entityId,
      style: style ?? this.style,
      size: size ?? this.size,
      position: position ?? this.position,
      roomId: roomId ?? this.roomId,
      forecastType: forecastType ?? this.forecastType,
      customTitle: customTitle ?? this.customTitle,
      showTitle: showTitle ?? this.showTitle,
    );
  }
}

/// 卡片编辑控制器
class CardEditorController extends GetxController {
  // 服务
  final StorageService _storageService = Get.find<StorageService>();
  final HaService _haService = Get.find<HaService>();

  // 存储实例
  final GetStorage _storage = GetStorage();

  // 卡片列表
  final RxList<CardModel> cards = <CardModel>[].obs;

  // 是否处于编辑模式
  final RxBool isEditMode = false.obs;

  // 当前选中的卡片样式
  final Rx<CardStyle> selectedCardStyle = CardStyle.defaultStyle.obs;

  // 当前选中的房间ID
  final Rx<String?> selectedRoomId = Rx<String?>(null);

  @override
  void onInit() {
    super.onInit();

    // 加载卡片
    _loadCards();

    // 监听房间变化
    ever(selectedRoomId, (_) {
      // 过滤卡片
      _filterCardsByRoom();
    });
  }

  /// 加载卡片（私有方法）
  void _loadCards() {
    try {
      final savedCards = _storage.read<List<dynamic>>('home_cards');

      if (savedCards != null) {
        cards.value = savedCards
            .map((item) => CardModel.fromJson(item as Map<String, dynamic>))
            .toList();

        // 按位置排序
        cards.sort((a, b) => a.position.compareTo(b.position));

        LogService.to.i('已加载 ${cards.length} 个卡片');
      } else {
        // 创建默认卡片
        _createDefaultCards();
      }
    } catch (e) {
      LogService.to.e('加载卡片失败', e);
      // 创建默认卡片
      _createDefaultCards();
    }
  }

  /// 加载卡片（公共方法）
  void loadCards() {
    _loadCards();
  }

  /// 创建默认卡片
  void _createDefaultCards() {
    LogService.to.i('创建默认卡片');

    cards.clear();

    // 添加天气卡片（每日预报）
    cards.add(CardModel(
      id: 'weather_daily_${DateTime.now().millisecondsSinceEpoch}',
      type: CardType.weather,
      position: 0,
      forecastType: WeatherForecastType.daily,
    ));

    // 添加灯光卡片（如果有灯光设备）
    final lights = _haService.getEntitiesByDomain('light');
    if (lights.isNotEmpty) {
      cards.add(CardModel(
        id: 'light_${DateTime.now().millisecondsSinceEpoch}',
        type: CardType.light,
        entityId: lights.first.entityId,
        position: 1,
      ));
    }

    // 添加传感器卡片（如果有传感器设备）
    final sensors = _haService.getSensorEntities();
    if (sensors.isNotEmpty) {
      cards.add(CardModel(
        id: 'sensor_${DateTime.now().millisecondsSinceEpoch}',
        type: CardType.sensor,
        entityId: sensors.first.entityId,
        position: 2,
        size: CardSize.small,
      ));
    }

    // 保存卡片
    _saveCards();
  }

  /// 保存卡片
  void _saveCards() {
    try {
      final cardsJson = cards.map((card) => card.toJson()).toList();
      _storage.write('home_cards', cardsJson);
      LogService.to.i('已保存 ${cards.length} 个卡片');
    } catch (e) {
      LogService.to.e('保存卡片失败', e);
    }
  }

  /// 过滤卡片（根据房间）
  void _filterCardsByRoom() {
    // 如果没有选中房间，显示所有卡片
    if (selectedRoomId.value == null) {
      _loadCards();
      return;
    }

    // 加载所有卡片
    final allCards = _storage.read<List<dynamic>>('home_cards');

    if (allCards != null) {
      // 过滤卡片
      final filteredCards = allCards
          .map((item) => CardModel.fromJson(item as Map<String, dynamic>))
          .where((card) => card.roomId == selectedRoomId.value)
          .toList();

      // 按位置排序
      filteredCards.sort((a, b) => a.position.compareTo(b.position));

      cards.value = filteredCards;

      LogService.to.i('已过滤 ${cards.length} 个卡片（房间ID: ${selectedRoomId.value}）');
    }
  }

  /// 添加卡片
  void addCard(CardModel card) {
    // 设置位置
    card = card.copyWith(position: cards.length);

    // 添加卡片
    cards.add(card);

    // 保存卡片
    _saveCards();

    LogService.to.i('已添加卡片: ${card.type}，房间ID: ${card.roomId ?? '全部房间'}');
  }

  /// 删除卡片
  void deleteCard(String id) {
    // 删除卡片
    cards.removeWhere((card) => card.id == id);

    // 更新位置
    for (var i = 0; i < cards.length; i++) {
      cards[i] = cards[i].copyWith(position: i);
    }

    // 保存卡片
    _saveCards();

    LogService.to.i('已删除卡片: $id');
  }

  /// 更新卡片
  void updateCard(CardModel card) {
    // 查找卡片索引
    final index = cards.indexWhere((item) => item.id == card.id);

    if (index != -1) {
      // 更新卡片
      cards[index] = card;

      // 保存卡片
      _saveCards();

      LogService.to.i('已更新卡片: ${card.id}');
    }
  }

  /// 更新卡片样式
  void updateCardStyle(String id, CardStyle style) {
    // 查找卡片索引
    final index = cards.indexWhere((item) => item.id == id);

    if (index != -1) {
      // 更新卡片样式
      cards[index] = cards[index].copyWith(style: style);

      // 保存卡片
      _saveCards();

      LogService.to.i('已更新卡片样式: $id -> $style');
    }
  }

  /// 更新卡片尺寸
  void updateCardSize(String id, CardSize size) {
    // 查找卡片索引
    final index = cards.indexWhere((item) => item.id == id);

    if (index != -1) {
      // 更新卡片尺寸
      cards[index] = cards[index].copyWith(size: size);

      // 保存卡片
      _saveCards();

      LogService.to.i('已更新卡片尺寸: $id -> $size');
    }
  }

  /// 更新卡片位置
  void updateCardPosition(String id, int position) {
    // 查找卡片索引
    final index = cards.indexWhere((item) => item.id == id);

    if (index != -1) {
      // 更新卡片位置
      cards[index] = cards[index].copyWith(position: position);

      // 保存卡片
      _saveCards();

      LogService.to.i('已更新卡片位置: $id -> $position');
    }
  }

  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode.value = !isEditMode.value;
    LogService.to.i('编辑模式: ${isEditMode.value}');
  }

  /// 设置卡片样式
  void setCardStyle(CardStyle style) {
    selectedCardStyle.value = style;
    LogService.to.i('已设置卡片样式: $style');
  }

  /// 设置房间ID
  void setRoomId(String? roomId) {
    selectedRoomId.value = roomId;
    LogService.to.i('已设置房间ID: $roomId');
  }
}
