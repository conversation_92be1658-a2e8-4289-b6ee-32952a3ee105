import 'package:flutter/material.dart';
import '../../../app/services/statistics_service.dart';

/// 使用频率热力图组件
class UsageHeatmap extends StatelessWidget {
  /// 使用频率数据
  final List<DeviceUsageData> data;
  
  const UsageHeatmap({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('暂无数据'),
      );
    }
    
    return Column(
      children: [
        // 图例
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLegendItem(context, '低', Colors.blue.shade100),
              const SizedBox(width: 16),
              _buildLegendItem(context, '中', Colors.blue.shade300),
              const SizedBox(width: 16),
              _buildLegendItem(context, '高', Colors.blue.shade700),
            ],
          ),
        ),
        
        // 时间轴
        Padding(
          padding: const EdgeInsets.only(left: 100, right: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('0时'),
              const Text('6时'),
              const Text('12时'),
              const Text('18时'),
              const Text('24时'),
            ],
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 热力图
        Expanded(
          child: ListView.builder(
            itemCount: data.length,
            itemBuilder: (context, index) {
              return _buildHeatmapRow(context, data[index]);
            },
          ),
        ),
      ],
    );
  }
  
  /// 构建图例项
  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(label),
      ],
    );
  }
  
  /// 构建热力图行
  Widget _buildHeatmapRow(BuildContext context, DeviceUsageData deviceData) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // 设备名称
          SizedBox(
            width: 100,
            child: Text(
              deviceData.deviceName,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
          ),
          
          // 热力图单元格
          Expanded(
            child: Row(
              children: List.generate(24, (hour) {
                final usage = deviceData.hourlyUsage[hour] ?? 0.0;
                return Expanded(
                  child: Container(
                    height: 24,
                    margin: const EdgeInsets.all(1),
                    decoration: BoxDecoration(
                      color: _getHeatmapColor(usage),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Tooltip(
                      message: '${deviceData.deviceName}\n${hour}时: ${(usage * 100).toStringAsFixed(0)}%',
                      child: const SizedBox.expand(),
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 获取热力图颜色
  Color _getHeatmapColor(double usage) {
    if (usage <= 0) {
      return Colors.grey.shade100;
    } else if (usage < 0.3) {
      return Colors.blue.shade100;
    } else if (usage < 0.6) {
      return Colors.blue.shade300;
    } else {
      return Colors.blue.shade700;
    }
  }
}
