```
lib/
├── main.dart                      # 应用程序入口点
├── app/                           # 应用核心配置
│   ├── bindings/                  # GetX 依赖注入绑定
│   │   └── initial_binding.dart   # 初始应用绑定
│   ├── config/                    # 应用配置
│   │   ├── app_constants.dart     # 常量定义
│   │   ├── app_pages.dart         # 路由页面定义
│   │   ├── app_routes.dart        # 路由名称定义
│   │   └── app_theme.dart         # 主题配置
│   ├── controllers/               # 全局控制器
│   │   ├── app_controller.dart    # 应用全局控制器
│   │   └── theme_controller.dart  # 主题控制器
│   ├── data/                      # 数据层
│   │   ├── local/                 # 本地存储 
│   │   │   ├── storage_keys.dart  # 存储键名定义
│   │   │   └── storage_service.dart # 存储服务
│   │   ├── models/                # 数据模型
│   │   │   ├── device_model.dart  # 设备模型
│   │   │   ├── entity_model.dart  # HA实体模型
│   │   │   ├── scene_model.dart   # 场景模型
│   │   │   ├── user_model.dart    # 用户模型
│   │   │   └── weather_model.dart # 天气模型
│   │   ├── providers/             # 数据提供者
│   │   │   ├── ha_provider.dart   # Home Assistant API
│   │   │   └── weather_provider.dart # 天气API
│   │   └── repositories/          # 数据仓库
│   │       ├── device_repository.dart # 设备仓库
│   │       ├── scene_repository.dart  # 场景仓库
│   │       └── weather_repository.dart # 天气仓库
│   ├── services/                  # 服务层
│   │   ├── ha_service.dart        # Home Assistant服务
│   │   ├── websocket_service.dart # WebSocket服务
│   │   ├── auth_service.dart      # 认证服务
│   │   ├── webdav_service.dart    # WebDAV同步服务
│   │   └── notification_service.dart # 通知服务
│   └── utils/                     # 工具类
│       ├── extensions/            # 扩展方法
│       │   ├── string_extensions.dart # 字符串扩展
│       │   └── widget_extensions.dart # 组件扩展
│       ├── helpers/               # 辅助函数
│       │   ├── date_helper.dart   # 日期处理
│       │   └── ui_helper.dart     # UI辅助
│       └── logger.dart            # 日志工具
├── modules/                       # 功能模块
│   ├── home/                      # 首页模块
│   │   ├── bindings/              # 首页绑定
│   │   │   └── home_binding.dart  # 首页依赖注入
│   │   ├── controllers/           # 首页控制器
│   │   │   ├── home_controller.dart # 首页主控制器
│   │   │   └── card_editor_controller.dart # 卡片编辑控制器
│   │   ├── views/                 # 首页视图
│   │   │   ├── home_view.dart     # 首页主视图
│   │   │   ├── card_editor_view.dart # 卡片编辑视图
│   │   │   └── components/        # 首页组件
│   │   │       ├── status_bar.dart # 顶部状态栏
│   │   │       └── room_selector.dart # 房间选择器
│   │   └── widgets/               # 首页小部件
│   │       └── cards/             # 卡片组件
│   │           ├── base_card.dart # 基础卡片
│   │           ├── weather_card.dart # 天气卡片
│   │           ├── calendar_card.dart # 日历卡片
│   │           ├── light_card.dart # 灯光卡片
│   │           ├── climate_card.dart # 温控卡片
│   │           ├── camera_card.dart # 摄像头卡片
│   │           ├── sensor_card.dart # 传感器卡片
│   │           ├── cover_card.dart # 窗帘卡片
│   │           ├── lock_card.dart # 门锁卡片
│   │           ├── media_card.dart # 媒体卡片
│   │           ├── purifier_card.dart # 净化器卡片
│   │           ├── vacuum_card.dart # 扫地机卡片
│   │           └── energy_card.dart # 能源卡片
│   ├── scenes/                    # 场景模块
│   │   ├── bindings/              # 场景绑定
│   │   │   └── scene_binding.dart # 场景依赖注入
│   │   ├── controllers/           # 场景控制器
│   │   │   ├── scenes_controller.dart # 场景列表控制器
│   │   │   └── scene_editor_controller.dart # 场景编辑控制器
│   │   ├── views/                 # 场景视图
│   │   │   ├── scenes_view.dart   # 场景列表视图
│   │   │   └── scene_editor_view.dart # 场景编辑视图
│   │   └── widgets/               # 场景小部件
│   │       └── scene_card.dart    # 场景卡片
│   ├── devices/                   # 设备模块
│   │   ├── bindings/              # 设备绑定
│   │   │   └── device_binding.dart # 设备依赖注入
│   │   ├── controllers/           # 设备控制器
│   │   │   ├── devices_controller.dart # 设备列表控制器
│   │   │   └── device_detail_controller.dart # 设备详情控制器
│   │   ├── views/                 # 设备视图
│   │   │   ├── devices_view.dart  # 设备列表视图
│   │   │   └── device_detail_view.dart # 设备详情视图
│   │   └── widgets/               # 设备小部件
│   │       └── device_list_item.dart # 设备列表项
│   └── profile/                   # 我的/设置模块
│       ├── bindings/              # 设置绑定
│       │   └── profile_binding.dart # 设置依赖注入
│       ├── controllers/           # 设置控制器
│       │   ├── profile_controller.dart # 设置主控制器
│       │   ├── statistics_controller.dart # 统计控制器
│       │   └── settings_controller.dart # 设置控制器
│       ├── views/                 # 设置视图
│       │   ├── profile_view.dart  # 设置主视图
│       │   ├── statistics_view.dart # 统计视图
│       │   └── settings_view.dart # 设置视图
│       └── widgets/               # 设置小部件
│           ├── energy_chart.dart  # 能源图表
│           └── usage_heatmap.dart # 使用热力图
└── shared/                        # 共享组件
    ├── widgets/                   # 共享小部件
    │   ├── custom_app_bar.dart    # 自定义应用栏
    │   ├── custom_bottom_nav.dart # 自定义底部导航
    │   ├── loading_indicator.dart # 加载指示器
    │   └── error_view.dart        # 错误视图
    └── themes/                    # 主题相关
        ├── app_colors.dart        # 应用颜色
        ├── app_text_styles.dart   # 文本样式
        └── card_styles.dart       # 卡片样式
```
