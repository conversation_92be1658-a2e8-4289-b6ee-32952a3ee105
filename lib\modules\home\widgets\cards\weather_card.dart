import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import 'package:fl_chart/fl_chart.dart';
import '../../../../app/services/log_service.dart';
import '../../controllers/weather_card_controller.dart';
import '../../models/weather_forecast_model.dart';
import '../../models/weather_model.dart';
import 'base_card.dart';

/// 预报视图类型枚举
enum ForecastViewType { list, chart }

/// 天气卡片
class WeatherCard extends StatefulWidget {
  /// 卡片ID
  final String id;

  /// 卡片样式
  final CardStyle style;

  /// 卡片尺寸
  final CardSize size;

  /// 是否处于编辑模式
  final bool editMode;

  /// 自定义标题
  final String? customTitle;

  /// 是否显示标题栏
  final bool showTitle;

  /// 删除事件
  final VoidCallback? onDelete;

  /// 编辑事件
  final VoidCallback? onEdit;

  /// 实体ID
  final String? entityId;

  /// 天气预报类型
  final WeatherForecastType? forecastType;

  const WeatherCard({
    super.key,
    required this.id,
    this.style = CardStyle.defaultStyle,
    this.size = CardSize.medium,
    this.editMode = false,
    this.customTitle,
    this.showTitle = true,
    this.onDelete,
    this.onEdit,
    this.entityId,
    this.forecastType,
  });

  @override
  State<WeatherCard> createState() => _WeatherCardState();
}

class _WeatherCardState extends State<WeatherCard> with SingleTickerProviderStateMixin {
  // 天气卡片控制器
  late WeatherCardController _cardController;
  
  // 动画控制器
  late AnimationController _animationController;
  
  // 各种动画
  late Animation<double> _rotationAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _sizeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // 删除内部枚举定义，使用外部的ForecastViewType枚举

  @override
  void initState() {
    super.initState();

    // 创建天气卡片控制器
    _cardController = Get.put(
      WeatherCardController(
        cardId: widget.id,
        entityId: widget.entityId ?? 'weather.forecast_wo_de_jia',
        forecastType: widget.forecastType,
      ),
      tag: widget.id, // 使用卡片ID作为标签，确保每个卡片有自己的控制器
    );
    
    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    // 旋转动画 - 用于晴天/太阳图标
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.linear,
      ),
    );
    
    // 弹跳动画 - 用于雨滴图标
    _bounceAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0, end: 1.2),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 0.8),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.0),
        weight: 1,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 透明度动画 - 用于云图标
    _opacityAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.6),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.6, end: 1.0),
        weight: 1,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 大小动画 - 用于雪花图标
    _sizeAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.2),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 0.9),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.9, end: 1.0),
        weight: 1,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 滑动动画 - 用于雷暴图标
    _slideAnimation = TweenSequence<Offset>([
      TweenSequenceItem(
        tween: Tween<Offset>(
          begin: const Offset(0, 0),
          end: const Offset(0.1, 0),
        ),
        weight: 1,
      ),
      TweenSequenceItem(
        tween: Tween<Offset>(
          begin: const Offset(0.1, 0),
          end: const Offset(-0.1, 0),
        ),
        weight: 2,
      ),
      TweenSequenceItem(
        tween: Tween<Offset>(
          begin: const Offset(-0.1, 0),
          end: const Offset(0, 0),
        ),
        weight: 1,
      ),
    ]).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 启动动画并循环
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    
    LogService.to.i('[${widget.id}] 销毁天气卡片组件');

    // 删除控制器
    if (Get.isRegistered<WeatherCardController>(tag: widget.id)) {
      // 先取消订阅，然后删除控制器
      final controller = Get.find<WeatherCardController>(tag: widget.id);
      controller.unsubscribeWeatherForecast().then((_) {
        if (Get.isRegistered<WeatherCardController>(tag: widget.id)) {
          Get.delete<WeatherCardController>(tag: widget.id);
          LogService.to.i('[${widget.id}] 已删除天气卡片控制器');
        }
      });
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final weather = _cardController.weather.value;
      final forecast = widget.forecastType != null
          ? _cardController.weatherForecast.value
          : null;
      final isLoading = _cardController.isLoading.value;

      // 如果没有天气数据或者需要预报数据但正在加载中，显示加载状态
      if (weather == null || (widget.forecastType != null && isLoading)) {
        return _buildLoadingCard(context);
      }

      // 构建卡片标题
      String title = weather.friendlyName;
      if (widget.forecastType != null) {
        title += ' (${widget.forecastType == WeatherForecastType.daily ? '每日' : '小时'}预报)';
      }

      // 如果需要预报数据但没有获取到，显示加载状态
      if (widget.forecastType != null && forecast == null) {
        return BaseCard(
          id: widget.id,
          title: title,
          customTitle: widget.customTitle,
          showTitle: widget.showTitle,
          icon: Icons.cloud,
          style: widget.style,
          size: widget.size,
          editMode: widget.editMode,
          onDelete: widget.onDelete,
          onEdit: widget.onEdit,
          content: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      return BaseCard(
        id: widget.id,
        title: title,
        customTitle: widget.customTitle,
        showTitle: widget.showTitle,
        icon: Icons.cloud,
        style: widget.style,
        size: widget.size,
        editMode: widget.editMode,
        onDelete: widget.onDelete,
        onEdit: widget.onEdit,
        onTap: () => _showWeatherDetails(context, weather, forecast),
        content: _buildCompactForecastContent(context, weather, forecast!),
      );
    });
  }

  /// 构建加载中卡片
  Widget _buildLoadingCard(BuildContext context) {
    return BaseCard(
      id: widget.id,
      title: '天气',
      customTitle: widget.customTitle,
      showTitle: widget.showTitle,
      icon: Icons.cloud,
      style: widget.style,
      size: widget.size,
      editMode: widget.editMode,
      onDelete: widget.onDelete,
      onEdit: widget.onEdit,
      content: LayoutBuilder(
        builder: (context, constraints) {
          // 根据可用高度调整布局
          final availableHeight = constraints.maxHeight;
          final isTightVertically = widget.showTitle && availableHeight < 130;
          final isVeryTight = availableHeight < 80;

          // 调整进度指示器大小
          final indicatorSize = isVeryTight ? 20.0 : (isTightVertically ? 24.0 : 32.0);

          // 调整字体大小
          final fontSize = isTightVertically ? 11.0 : 13.0;

          // 调整间距
          final spacing = isTightVertically ? 4.0 : 8.0;

          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: indicatorSize,
                  height: indicatorSize,
                  child: CircularProgressIndicator(
                    strokeWidth: isTightVertically ? 2.0 : 3.0,
                  ),
                ),
                // 只有在空间足够或非常小的卡片上才显示文本
                if (!isVeryTight && (!isTightVertically || availableHeight > 100)) ...[
                  SizedBox(height: spacing),
                  Text(
                    '加载天气数据...',
                    style: TextStyle(
                      fontSize: fontSize,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建紧凑版天气预报内容（只显示第一个预报项）- 左右布局
  Widget _buildCompactForecastContent(BuildContext context, WeatherModel weather, WeatherForecastModel forecast) {
    // 如果预报为空，显示加载状态
    if (forecast.forecast.isEmpty) {
      return Center(
        child: Text('暂无预报数据'),
      );
    }

    // 获取第一个预报项
    final firstItem = forecast.forecast.first;

    // 使用LayoutBuilder来获取可用空间
    return LayoutBuilder(
      builder: (context, constraints) {
        // 判断是否是小卡片
        final isSmallCard = widget.size == CardSize.small;
        final isMediumCard = widget.size == CardSize.medium;
        final isLargeCard = widget.size == CardSize.large;
        
        // 可用宽度和高度
        final availableWidth = constraints.maxWidth;
        final availableHeight = constraints.maxHeight;
        
        // 判断空间是否紧凑
        final isTightVertically = widget.showTitle && availableHeight < 130;
        
        // 根据卡片尺寸和是否显示标题调整布局
        final baseFontSize = isSmallCard ? 10.0 : (isMediumCard ? 12.0 : 14.0);
        final labelFontSize = baseFontSize;
        final valueFontSize = baseFontSize * 1.2;
        final smallFontSize = baseFontSize * 0.8;
        
        // 根据可用高度调整图标大小
        final iconSize = isTightVertically 
            ? (isSmallCard ? 36.0 : (isMediumCard ? 46.0 : 52.0))
            : (isSmallCard ? 42.0 : (isMediumCard ? 56.0 : 64.0));
        final smallIconSize = baseFontSize * 1.2;

        // 格式化日期
        final dateFormat = forecast.type == WeatherForecastType.daily
            ? DateFormat('MM/dd')
            : DateFormat('HH:mm');
        final dateText = dateFormat.format(firstItem.dateTime.toLocal());

        // 垂直和水平间距根据空间紧凑程度调整
        final verticalPadding = isTightVertically ? 4.0 : (isSmallCard ? 6.0 : 8.0);
        final verticalSpacing = isTightVertically ? 3.0 : (isSmallCard ? 4.0 : 6.0);

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 左侧：天气图标和天气状态
            Container(
              width: availableWidth * 0.38, // 占总宽度的38%
              padding: EdgeInsets.symmetric(
                horizontal: 8.0, 
                vertical: verticalPadding
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 天气图标 - 添加动画
                  _buildAnimatedWeatherIcon(
                    firstItem.condition, 
                    iconSize, 
                    _getWeatherColor(firstItem.condition)
                  ),
                  
                  // 只有在有足够空间时才显示天气状况文本
                  if (!isTightVertically || isLargeCard) ...[
                    SizedBox(height: 2),
                    // 天气状况
                    Text(
                      _getWeatherText(firstItem.condition),
                      style: TextStyle(
                        fontSize: labelFontSize,
                        fontWeight: FontWeight.bold,
                        color: _getWeatherColor(firstItem.condition),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // 右侧：天气信息
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 8.0, 
                  vertical: verticalPadding
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 预报类型和日期
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 6.0,
                        vertical: 2.0,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            forecast.type == WeatherForecastType.daily
                                ? Icons.calendar_today
                                : Icons.access_time,
                            size: smallIconSize,
                            color: Colors.blue,
                          ),
                          SizedBox(width: 4),
                          Text(
                            '$dateText',
                            style: TextStyle(
                              fontSize: smallFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: verticalSpacing),

                    // 温度信息
                    Row(
                      children: [
                        Icon(
                          Icons.thermostat,
                          size: smallIconSize,
                          color: Colors.orange,
                        ),
                        SizedBox(width: 4),
                        Text(
                          forecast.type == WeatherForecastType.daily && firstItem.tempLow != null
                              ? '${firstItem.temperature.toStringAsFixed(0)}/${firstItem.tempLow!.toStringAsFixed(0)}${weather.temperatureUnit}'
                              : '${firstItem.temperature.toStringAsFixed(1)}${weather.temperatureUnit}',
                          style: TextStyle(
                            fontSize: valueFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),

                    SizedBox(height: verticalSpacing),

                    // 湿度和风速信息
                    // 如果空间很紧凑，只显示湿度
                    Row(
                      children: [
                        // 湿度
                        Icon(
                          Icons.water_drop,
                          size: smallIconSize,
                          color: Colors.blue,
                        ),
                        SizedBox(width: 4),
                        Text(
                          '${firstItem.humidity}%',
                          style: TextStyle(
                            fontSize: labelFontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        // 只有在有足够空间时才显示风速
                        if (!isTightVertically && availableWidth > 200) ...[
                          SizedBox(width: 12),
                          Icon(
                            Icons.air,
                            size: smallIconSize,
                            color: Colors.blueGrey,
                          ),
                          SizedBox(width: 4),
                          Text(
                            '${firstItem.windSpeed.toStringAsFixed(1)}${weather.windSpeedUnit}',
                            style: TextStyle(
                              fontSize: labelFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.blueGrey,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// 构建带动画的天气图标
  Widget _buildAnimatedWeatherIcon(String condition, double size, Color color) {
    final lowerCondition = condition.toLowerCase();
    
    // 根据天气状况使用不同的动画效果
    if (lowerCondition == 'sunny' || lowerCondition == 'clear') {
      // 旋转动画 - 太阳旋转效果
      return AnimatedBuilder(
        animation: _rotationAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationAnimation.value,
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: color,
            ),
          );
        },
      );
    } else if (lowerCondition == 'rainy') {
      // 弹跳动画 - 雨滴下落效果
      return AnimatedBuilder(
        animation: _bounceAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, 2 * _bounceAnimation.value),
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: color,
            ),
          );
        },
      );
    } else if (lowerCondition == 'cloudy' || lowerCondition == 'partlycloudy') {
      // 透明度动画 - 云飘动效果
      return AnimatedBuilder(
        animation: _opacityAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: color,
            ),
          );
        },
      );
    } else if (lowerCondition == 'snowy') {
      // 大小动画 - 雪花闪烁效果
      return AnimatedBuilder(
        animation: _sizeAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _sizeAnimation.value,
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: color,
            ),
          );
        },
      );
    } else if (lowerCondition == 'stormy') {
      // 滑动动画 - 雷暴闪电效果
      return AnimatedBuilder(
        animation: _slideAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: _slideAnimation.value,
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: color,
            ),
          );
        },
      );
    } else {
      // 默认无动画
      return Icon(
        _getWeatherIcon(condition),
        size: size,
        color: color,
      );
    }
  }
  
  /// 在详情页面使用的动画天气图标
  Widget _buildDetailAnimatedWeatherIcon(String condition, double size, Color color) {
    // 为详情页面使用更复杂的组合动画效果
    final lowerCondition = condition.toLowerCase();
    
    if (lowerCondition == 'sunny' || lowerCondition == 'clear') {
      // 太阳图标 - 旋转并有发光效果
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withOpacity(0.3 + 0.2 * _sizeAnimation.value),
                  blurRadius: 20 * _sizeAnimation.value,
                  spreadRadius: 5 * _sizeAnimation.value,
                ),
              ],
            ),
            child: Transform.rotate(
              angle: _rotationAnimation.value,
              child: Icon(
                _getWeatherIcon(condition),
                size: size,
                color: color,
              ),
            ),
          );
        },
      );
    } else if (lowerCondition == 'cloudy' || lowerCondition == 'partlycloudy') {
      // 云图标 - 横向漂浮效果
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(3 * math.sin(_animationController.value * 2 * math.pi), 0),
            child: Opacity(
              opacity: 0.7 + 0.3 * _opacityAnimation.value,
              child: Icon(
                _getWeatherIcon(condition),
                size: size,
                color: color,
              ),
            ),
          );
        },
      );
    } else if (lowerCondition == 'rainy') {
      // 雨滴图标 - 下落和波动效果
      return Stack(
        alignment: Alignment.center,
        children: [
          // 主图标有弹跳效果
          AnimatedBuilder(
            animation: _bounceAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 2 * _bounceAnimation.value),
                child: Icon(
                  _getWeatherIcon(condition),
                  size: size,
                  color: color,
                ),
              );
            },
          ),
          // 添加一个微小的水纹效果
          AnimatedBuilder(
            animation: _opacityAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _opacityAnimation.value * 0.3,
                child: Container(
                  width: size * 1.2,
                  height: size * 1.2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.5),
                      width: 2,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      );
    } else if (lowerCondition == 'snowy') {
      // 雪花图标 - 旋转并有大小变化
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.rotate(
            angle: _animationController.value * math.pi,
            child: Transform.scale(
              scale: 0.9 + 0.2 * _sizeAnimation.value,
              child: Icon(
                _getWeatherIcon(condition),
                size: size,
                color: color,
              ),
            ),
          );
        },
      );
    } else if (lowerCondition == 'stormy') {
      // 雷暴图标 - 闪烁和抖动效果
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final flash = _animationController.value > 0.7 && _animationController.value < 0.8 ||
                        _animationController.value > 0.4 && _animationController.value < 0.45;
                        
          return Transform.translate(
            offset: Offset(
              2 * math.sin(_animationController.value * 10 * math.pi),
              1 * math.cos(_animationController.value * 15 * math.pi),
            ),
            child: Icon(
              _getWeatherIcon(condition),
              size: size,
              color: flash ? Colors.yellow : color,
            ),
          );
        },
      );
    } else {
      // 默认图标
      return Icon(
        _getWeatherIcon(condition),
        size: size,
        color: color,
      );
    }
  }
  
  @override
  void didUpdateWidget(WeatherCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查关键属性是否发生变化
    if (widget.forecastType != oldWidget.forecastType ||
        widget.entityId != oldWidget.entityId ||
        widget.id != oldWidget.id) {

      LogService.to.i('[${widget.id}] 天气卡片属性变化: '
          'forecastType=${widget.forecastType?.toString() ?? "null"} (原: ${oldWidget.forecastType?.toString() ?? "null"}), '
          'entityId=${widget.entityId} (原: ${oldWidget.entityId}), '
          'id=${widget.id} (原: ${oldWidget.id})');

      // 先取消旧控制器的订阅
      if (Get.isRegistered<WeatherCardController>(tag: oldWidget.id)) {
        final oldController = Get.find<WeatherCardController>(tag: oldWidget.id);
        // 确保取消订阅
        oldController.unsubscribeWeatherForecast().then((_) {
          // 删除旧控制器
          Get.delete<WeatherCardController>(tag: oldWidget.id);
          LogService.to.i('[${oldWidget.id}] 已删除旧的天气卡片控制器');

          // 确保旧控制器被删除后再创建新控制器
          if (!Get.isRegistered<WeatherCardController>(tag: widget.id)) {
            _createNewController();
          }
        });
      } else {
        // 如果没有旧控制器，直接创建新控制器
        _createNewController();
      }
    }
  }
  
  /// 创建新的天气卡片控制器
  void _createNewController() {
    LogService.to.i('[${widget.id}] 创建新的天气卡片控制器');

    // 创建新控制器
    _cardController = Get.put(
      WeatherCardController(
        cardId: widget.id,
        entityId: widget.entityId ?? 'weather.forecast_wo_de_jia',
        forecastType: widget.forecastType,
      ),
      tag: widget.id,
    );
  }

  /// 显示天气详情
  void _showWeatherDetails(BuildContext context, WeatherModel weather, [WeatherForecastModel? forecast]) {
    // 如果是预报类型但没有预报数据，尝试从控制器获取
    if (widget.forecastType != null && forecast == null) {
      forecast = _cardController.weatherForecast.value;
    }

    // 确保forecast不为null且有预报数据
    if (forecast == null || forecast.forecast.isEmpty) {
      // 如果没有预报数据，显示提示并返回
      Get.snackbar(
        '提示',
        '暂无预报数据',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // 创建一个状态控制器，用于跟踪选中的预报项
    // 默认选中第一个预报项(索引为1)
    final selectedForecastIndex = ValueNotifier<int>(1);
    
    // 创建一个状态控制器，用于跟踪当前视图类型
    // 修改默认视图为图表视图
    final currentViewType = ValueNotifier<ForecastViewType>(ForecastViewType.chart);

    // 创建一个状态控制器，用于存储当前显示的天气信息
    final Map<String, dynamic> initialWeatherInfo;

    // 显示第一个预报项的数据
    final firstForecastItem = forecast.forecast.first;
    initialWeatherInfo = {
      'condition': firstForecastItem.condition,
      'temperature': firstForecastItem.temperature,
      'temperatureUnit': weather.temperatureUnit,
      'humidity': firstForecastItem.humidity,
      'windSpeed': firstForecastItem.windSpeed,
      'windSpeedUnit': weather.windSpeedUnit,
      'pressure': weather.pressure,
      'pressureUnit': weather.pressureUnit,
      'dewPoint': weather.dewPoint,
      'cloudCoverage': firstForecastItem.cloudCoverage,
      'uvIndex': firstForecastItem.uvIndex,
      'windBearing': firstForecastItem.windBearing,
      'precipitation': firstForecastItem.precipitation,
      'tempLow': firstForecastItem.tempLow,
      'dateTime': firstForecastItem.dateTime,
      'lastUpdated': forecast.lastUpdated,
    };

    final currentWeatherInfo = ValueNotifier<Map<String, dynamic>>(initialWeatherInfo);

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                weather.friendlyName,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // 天气详情 - 使用ValueListenableBuilder监听变化
              ValueListenableBuilder<Map<String, dynamic>>(
                valueListenable: currentWeatherInfo,
                builder: (context, weatherInfo, child) {
                  // 获取当前显示的天气信息
                  final condition = weatherInfo['condition'] as String;
                  final temperature = weatherInfo['temperature'] as double;
                  final temperatureUnit = weatherInfo['temperatureUnit'] as String;
                  final humidity = weatherInfo['humidity'] as int;
                  final windSpeed = weatherInfo['windSpeed'] as double;
                  final windSpeedUnit = weatherInfo['windSpeedUnit'] as String;
                  final pressure = weatherInfo['pressure'] as double;
                  final pressureUnit = weatherInfo['pressureUnit'] as String;
                  final dewPoint = weatherInfo['dewPoint'] as double?;
                  // 处理cloudCoverage可能是double或int的情况
                  final cloudCoverage = weatherInfo['cloudCoverage'] != null
                      ? (weatherInfo['cloudCoverage'] is int
                          ? weatherInfo['cloudCoverage'] as int
                          : (weatherInfo['cloudCoverage'] as double).round())
                      : null;
                  final uvIndex = weatherInfo['uvIndex'] as double?;
                  final windBearing = weatherInfo['windBearing'] as double?;
                  final lastUpdated = weatherInfo['lastUpdated'] as DateTime;

                  // 显示日期时间（如果是预报项）
                  final dateTime = weatherInfo['dateTime'] as DateTime?;
                  final isCurrentWeather = dateTime == null;

                  return Column(
                    children: [
                      // 日期时间（如果是预报项）
                      if (!isCurrentWeather) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(50),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            forecast!.type == WeatherForecastType.daily
                                ? DateFormat('yyyy-MM-dd').format(dateTime)
                                : DateFormat('MM-dd HH:mm').format(dateTime),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // 天气图标和温度
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: _getWeatherColor(condition).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          children: [
                            // 天气图标
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildAnimatedWeatherIcon(
                                  condition, 
                                  80, 
                                  _getWeatherColor(condition)
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _getWeatherText(condition),
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: _getWeatherColor(condition),
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(width: 20),

                            // 天气信息
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 温度
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        temperature.toStringAsFixed(1),
                                        style: const TextStyle(
                                          fontSize: 42,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        temperatureUnit,
                                        style: const TextStyle(
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),

                                  // 最低温度（如果有）
                                  if (weatherInfo['tempLow'] != null)
                                    Text(
                                      '最低: ${(weatherInfo['tempLow'] as double).toStringAsFixed(1)}$temperatureUnit',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.blue[700],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // 详细信息标题
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 20,
                            color: Colors.grey[700],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '详细信息',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 8),

                      // 详细信息
                      _buildDetailItem(context, '湿度', '$humidity%', Icons.water_drop, Colors.blue),
                      _buildDetailItem(context, '风速', '$windSpeed $windSpeedUnit', Icons.air, Colors.blueGrey),
                      _buildDetailItem(context, '气压', '$pressure $pressureUnit', Icons.compress, Colors.purple),

                      // 降水量（如果有且不为0）
                      if (weatherInfo['precipitation'] != null && (weatherInfo['precipitation'] as double) > 0)
                        _buildDetailItem(
                          context,
                          '降水量',
                          '${(weatherInfo['precipitation'] as double).toStringAsFixed(1)} mm',
                          Icons.water,
                          Colors.lightBlue
                        ),

                      // 露点（如果有）
                      if (dewPoint != null)
                        _buildDetailItem(context, '露点', '${dewPoint.toStringAsFixed(1)} $temperatureUnit', Icons.opacity, Colors.lightBlue),

                      // 云覆盖率（如果有）
                      if (cloudCoverage != null)
                        _buildDetailItem(context, '云覆盖率', '$cloudCoverage%', Icons.cloud, Colors.blueGrey),

                      // 紫外线指数（如果有）
                      if (uvIndex != null)
                        _buildDetailItem(context, '紫外线指数', uvIndex.toStringAsFixed(1), Icons.wb_sunny, Colors.amber),

                      // 风向（如果有）
                      if (windBearing != null)
                        _buildDetailItem(context, '风向', _getWindDirection(windBearing), Icons.navigation, Colors.indigo),

                      // 最后更新时间
                      _buildDetailItem(
                        context,
                        '更新时间',
                        isCurrentWeather
                            ? '${lastUpdated.hour}:${lastUpdated.minute.toString().padLeft(2, '0')}'
                            : '预报数据',
                        Icons.update,
                        Colors.grey
                      ),
                    ],
                  );
                },
              ),

              // 天气预报
              ...[
                const SizedBox(height: 24),

                // 预报标题
                Row(
                  children: [
                    Icon(
                      forecast.type == WeatherForecastType.daily
                          ? Icons.calendar_today
                          : Icons.access_time,
                      size: 24,
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '${forecast.type == WeatherForecastType.daily ? '每日' : '小时'}预报',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    
                    // 视图切换按钮 - 交换图表和列表按钮的位置
                    ValueListenableBuilder<ForecastViewType>(
                      valueListenable: currentViewType,
                      builder: (context, viewType, child) {
                        return Row(
                          children: [
                            // 图表视图按钮
                            IconButton(
                              icon: Icon(
                                Icons.show_chart,
                                color: viewType == ForecastViewType.chart 
                                    ? Colors.blue 
                                    : Colors.grey,
                              ),
                              onPressed: () {
                                currentViewType.value = ForecastViewType.chart;
                              },
                              tooltip: '图表视图',
                            ),
                            
                            // 列表视图按钮
                            IconButton(
                              icon: Icon(
                                Icons.view_list,
                                color: viewType == ForecastViewType.list 
                                    ? Colors.blue 
                                    : Colors.grey,
                              ),
                              onPressed: () {
                                currentViewType.value = ForecastViewType.list;
                              },
                              tooltip: '列表视图',
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 预报内容 - 根据视图类型显示不同的内容
                ValueListenableBuilder<ForecastViewType>(
                  valueListenable: currentViewType,
                  builder: (context, viewType, child) {
                    if (viewType == ForecastViewType.chart) {
                      // 图表视图
                      return _buildForecastChart(context, forecast!, weather);
                    } else {
                      // 列表视图
                      return SizedBox(
                        height: 150,
                        child: ValueListenableBuilder<int>(
                          valueListenable: selectedForecastIndex,
                          builder: (context, selectedIndex, child) {
                            // 如果没有预报数据，显示空列表
                            if (forecast == null || forecast.forecast.isEmpty) {
                              return const Center(
                                child: Text('暂无预报数据'),
                              );
                            }

                            // 此时我们已经确定forecast不为null
                            final nonNullForecast = forecast;
                            return ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: nonNullForecast.forecast.length,
                              itemBuilder: (context, index) {
                                final item = nonNullForecast.forecast[index];
                                final isSelected = selectedIndex == index + 1; // +1 因为索引从1开始

                                // 格式化日期
                                final dateFormat = nonNullForecast.type == WeatherForecastType.daily
                                    ? DateFormat('MM/dd')
                                    : DateFormat('HH:mm');
                                final dateText = dateFormat.format(item.dateTime.toLocal());

                                // 格式化星期
                                final weekdayFormat = DateFormat('EEE');
                                final weekdayText = weekdayFormat.format(item.dateTime.toLocal());

                                return GestureDetector(
                                  onTap: () {
                                    // 更新选中的预报项
                                    selectedForecastIndex.value = index + 1;

                                    // 更新显示的天气信息
                                    currentWeatherInfo.value = {
                                      'condition': item.condition,
                                      'temperature': item.temperature,
                                      'temperatureUnit': weather.temperatureUnit,
                                      'humidity': item.humidity,
                                      'windSpeed': item.windSpeed,
                                      'windSpeedUnit': weather.windSpeedUnit,
                                      'pressure': weather.pressure,
                                      'pressureUnit': weather.pressureUnit,
                                      'dewPoint': weather.dewPoint,
                                      'cloudCoverage': item.cloudCoverage,
                                      'uvIndex': item.uvIndex,
                                      'windBearing': item.windBearing,
                                      'precipitation': item.precipitation,
                                      'tempLow': item.tempLow,
                                      'dateTime': item.dateTime,
                                      'lastUpdated': nonNullForecast.lastUpdated,
                                    };
                                  },
                                  child: Container(
                                    width: 100,
                                    margin: const EdgeInsets.only(right: 12),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? _getWeatherColor(item.condition).withOpacity(0.15)
                                          : Theme.of(context).cardColor,
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: isSelected
                                          ? [
                                              BoxShadow(
                                                color: _getWeatherColor(item.condition).withOpacity(0.3),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              )
                                            ]
                                          : [
                                              BoxShadow(
                                                color: Colors.grey.withOpacity(0.1),
                                                blurRadius: 4,
                                                offset: const Offset(0, 1),
                                              )
                                            ],
                                      border: Border.all(
                                        color: isSelected
                                            ? _getWeatherColor(item.condition)
                                            : Theme.of(context).dividerColor,
                                        width: isSelected ? 2 : 1,
                                      ),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        // 日期
                                        Text(
                                          dateText,
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: isSelected ? _getWeatherColor(item.condition) : null,
                                          ),
                                        ),

                                        // 星期（仅每日预报）
                                        if (nonNullForecast.type == WeatherForecastType.daily)
                                          Text(
                                            weekdayText,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: isSelected
                                                  ? _getWeatherColor(item.condition).withOpacity(0.8)
                                                  : Theme.of(context).textTheme.bodySmall?.color,
                                            ),
                                          ),

                                        const SizedBox(height: 8),

                                        // 天气图标
                                        _buildAnimatedWeatherIcon(
                                          item.condition, 
                                          32, 
                                          isSelected
                                              ? _getWeatherColor(item.condition)
                                              : _getWeatherColor(item.condition).withOpacity(0.7)
                                        ),

                                        const SizedBox(height: 8),

                                        // 温度
                                        Text(
                                          nonNullForecast.type == WeatherForecastType.daily && item.tempLow != null
                                              ? '${item.temperature.toStringAsFixed(0)}/${item.tempLow!.toStringAsFixed(0)}${weather.temperatureUnit}'
                                              : '${item.temperature.toStringAsFixed(1)}${weather.temperatureUnit}',
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: isSelected ? _getWeatherColor(item.condition) : null,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      );
                    }
                  },
                ),
              ],

              const SizedBox(height: 24),

              // 关闭按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    '关闭',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取风向文本
  String _getWindDirection(double bearing) {
    const directions = ['北', '东北', '东', '东南', '南', '西南', '西', '西北'];
    final index = ((bearing + 22.5) % 360 / 45).floor();
    return directions[index];
  }

  /// 构建详细信息项
  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10.0),
            ),
            child: Icon(
              icon,
              size: 22,
              color: color,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取天气图标
  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return Icons.wb_sunny;
      case 'cloudy':
      case 'partlycloudy':
        return Icons.cloud;
      case 'rainy':
        return Icons.water_drop;
      case 'snowy':
        return Icons.ac_unit;
      case 'stormy':
        return Icons.thunderstorm;
      default:
        return Icons.wb_sunny;
    }
  }

  /// 获取天气颜色
  Color _getWeatherColor(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return Colors.orange;
      case 'cloudy':
      case 'partlycloudy':
        return Colors.blueGrey;
      case 'rainy':
        return Colors.blue;
      case 'snowy':
        return Colors.lightBlue;
      case 'stormy':
        return Colors.deepPurple;
      default:
        return Colors.orange;
    }
  }

  /// 获取天气文本
  String _getWeatherText(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return '晴天';
      case 'clear':
        return '晴朗';
      case 'cloudy':
        return '多云';
      case 'partlycloudy':
        return '局部多云';
      case 'rainy':
        return '雨天';
      case 'snowy':
        return '雪天';
      case 'stormy':
        return '雷暴';
      default:
        return condition;
    }
  }

  /// 构建天气预报折线图
  Widget _buildForecastChart(BuildContext context, WeatherForecastModel forecast, WeatherModel weather) {
    // 确保有足够的数据
    if (forecast.forecast.isEmpty) {
      return const Center(
        child: Text('暂无预报数据'),
      );
    }

    // 准备温度数据
    final temperatureSpots = <FlSpot>[];
    final tempLowSpots = <FlSpot>[];
    final humiditySpots = <FlSpot>[];
    final dateLabels = <String>[];
    
    // 确定是否有最低温度数据
    final hasLowTemp = forecast.type == WeatherForecastType.daily && 
                       forecast.forecast.any((item) => item.tempLow != null);
                       
    // 确定数据范围
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    // 最多显示10个数据点，避免图表过于拥挤
    final displayCount = math.min(forecast.forecast.length, 10);
    
    // 处理数据
    for (int i = 0; i < displayCount; i++) {
      final item = forecast.forecast[i];
      
      // X轴位置
      final x = i.toDouble();
      
      // 温度数据
      final tempY = item.temperature;
      temperatureSpots.add(FlSpot(x, tempY));
      
      // 更新Y轴范围
      if (tempY < minY) minY = tempY;
      if (tempY > maxY) maxY = tempY;
      
      // 最低温度数据（如果有）
      if (item.tempLow != null) {
        final lowTempY = item.tempLow!;
        tempLowSpots.add(FlSpot(x, lowTempY));
        
        // 更新Y轴范围
        if (lowTempY < minY) minY = lowTempY;
        if (lowTempY > maxY) maxY = lowTempY;
      }
      
      // 湿度数据（转换为与温度相同的比例）
      final humidityY = item.humidity * (maxY - minY) / 100 + minY;
      humiditySpots.add(FlSpot(x, humidityY));
      
      // 日期标签
      final dateFormat = forecast.type == WeatherForecastType.daily
          ? DateFormat('MM/dd')
          : DateFormat('HH:mm');
      dateLabels.add(dateFormat.format(item.dateTime.toLocal()));
    }
    
    // 确保Y轴范围合理
    final yRange = maxY - minY;
    minY = minY - yRange * 0.1; // 下方留出10%的空间
    maxY = maxY + yRange * 0.1; // 上方留出10%的空间
    
    // 构建折线图
    return Container(
      height: 220,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图例
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildChartLegendItem('温度', Colors.orange),
              const SizedBox(width: 16),
              if (hasLowTemp) ...[
                _buildChartLegendItem('最低温度', Colors.blue),
                const SizedBox(width: 16),
              ],
              _buildChartLegendItem('湿度', Colors.green),
            ],
          ),
          const SizedBox(height: 16),
          
          // 折线图
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: 5,
                  verticalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withOpacity(0.2),
                      strokeWidth: 1,
                    );
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(
                      color: Colors.grey.withOpacity(0.2),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        if (value.toInt() >= 0 && value.toInt() < dateLabels.length) {
                          return SideTitleWidget(
                            meta: meta,
                            child: Text(
                              dateLabels[value.toInt()],
                              style: TextStyle(
                                color: Colors.grey[700],
                                fontSize: 10,
                              ),
                            ),
                          );
                        }
                        return const SizedBox();
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 5,
                      getTitlesWidget: (value, meta) {
                        return SideTitleWidget(
                          meta: meta,
                          child: Text(
                            value.toStringAsFixed(0),
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 10,
                            ),
                          ),
                        );
                      },
                      reservedSize: 30,
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: Colors.grey.withOpacity(0.2)),
                ),
                minX: 0,
                maxX: (displayCount - 1).toDouble(),
                minY: minY,
                maxY: maxY,
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    // 修复最后一个点的提示框被遮挡的问题
                    fitInsideHorizontally: true,
                    fitInsideVertically: true,
                    getTooltipItems: (touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 && index < forecast.forecast.length) {
                          final item = forecast.forecast[index];
                          String text;
                          
                          if (spot.barIndex == 0) {
                            // 温度线
                            text = '温度: ${item.temperature.toStringAsFixed(1)}${weather.temperatureUnit}';
                          } else if (spot.barIndex == 1 && hasLowTemp) {
                            // 最低温度线
                            text = '最低: ${item.tempLow?.toStringAsFixed(1) ?? ''}${weather.temperatureUnit}';
                          } else {
                            // 湿度线
                            text = '湿度: ${item.humidity}%';
                          }
                          
                          return LineTooltipItem(
                            text,
                            TextStyle(
                              color: spot.barIndex == 0 
                                  ? Colors.orange 
                                  : (spot.barIndex == 1 && hasLowTemp ? Colors.blue : Colors.green),
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
                lineBarsData: [
                  // 温度线
                  LineChartBarData(
                    spots: temperatureSpots,
                    isCurved: true,
                    color: Colors.orange,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: Colors.orange,
                          strokeWidth: 1,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.orange.withOpacity(0.1),
                    ),
                  ),
                  
                  // 最低温度线（如果有）
                  if (hasLowTemp && tempLowSpots.isNotEmpty)
                    LineChartBarData(
                      spots: tempLowSpots,
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 3,
                            color: Colors.blue,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      dashArray: [5, 5], // 虚线效果
                    ),
                  
                  // 湿度线
                  LineChartBarData(
                    spots: humiditySpots,
                    isCurved: true,
                    color: Colors.green,
                    barWidth: 2,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: false,
                    ),
                    dashArray: [3, 3], // 虚线效果
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建图表图例项
  Widget _buildChartLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }
}
