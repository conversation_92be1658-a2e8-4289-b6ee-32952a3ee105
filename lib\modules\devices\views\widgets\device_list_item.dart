import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/data/models/entity_model.dart';
import '../../controllers/devices_controller.dart';

/// 设备列表项
class DeviceListItem extends StatelessWidget {
  final EntityModel entity;
  final VoidCallback onToggle;
  final VoidCallback onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onSetAlias;
  final bool isFavorite;
  final String? alias;

  const DeviceListItem({
    super.key,
    required this.entity,
    required this.onToggle,
    required this.onTap,
    this.onFavoriteToggle,
    this.onSetAlias,
    this.isFavorite = false,
    this.alias,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // 图标
              _buildEntityIcon(context),
              const SizedBox(width: 16.0),

              // 名称和状态
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // 收藏图标
                        if (isFavorite)
                          Padding(
                            padding: const EdgeInsets.only(right: 4.0),
                            child: Icon(
                              Icons.star,
                              size: 16.0,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),

                        // 设备名称（优先显示别名）
                        Expanded(
                          child: Text(
                            alias ?? entity.friendlyName,
                            style: Theme.of(context).textTheme.titleMedium,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    // 如果有别名，显示原始名称
                    if (alias != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Text(
                          entity.friendlyName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                    const SizedBox(height: 4.0),
                    Text(
                      _getStateText(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getStateColor(context),
                      ),
                    ),
                  ],
                ),
              ),

              // 更多操作按钮
              if (onFavoriteToggle != null || onSetAlias != null)
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    if (value == 'favorite' && onFavoriteToggle != null) {
                      onFavoriteToggle!();
                    } else if (value == 'alias' && onSetAlias != null) {
                      onSetAlias!();
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'favorite',
                      child: Row(
                        children: [
                          Icon(
                            isFavorite ? Icons.star : Icons.star_border,
                            size: 20.0,
                            color: isFavorite ? Theme.of(context).colorScheme.primary : null,
                          ),
                          const SizedBox(width: 8.0),
                          Text(isFavorite ? '取消收藏' : '收藏'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'alias',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20.0),
                          SizedBox(width: 8.0),
                          Text('设置别名'),
                        ],
                      ),
                    ),
                  ],
                ),

              // 控制按钮
              _buildControlButton(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建实体图标
  Widget _buildEntityIcon(BuildContext context) {
    return Container(
      width: 48.0,
      height: 48.0,
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(context),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Icon(
        _getEntityIcon(),
        color: _getIconColor(context),
        size: 24.0,
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton(BuildContext context) {
    if (entity.supportsTurnOn) {
      return Switch(
        value: entity.isOn,
        onChanged: (_) => onToggle(),
      );
    } else if (entity.isSensor) {
      return Icon(
        Icons.arrow_forward_ios,
        size: 16.0,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  /// 获取实体图标
  IconData _getEntityIcon() {
    // 如果实体有自定义图标，优先使用
    if (entity.icon != null) {
      // 这里应该将HA的mdi图标转换为Flutter图标
      // 由于无法直接映射，使用一些常见的映射
      final iconName = entity.icon!.replaceAll('mdi:', '');

      switch (iconName) {
        case 'lightbulb':
          return Icons.lightbulb;
        case 'lightbulb-outline':
          return Icons.lightbulb_outline;
        case 'fan':
          return Icons.air;
        case 'thermometer':
          return Icons.thermostat;
        case 'water-percent':
          return Icons.water_drop;
        case 'gauge':
          return Icons.speed;
        case 'power-socket':
          return Icons.power;
        case 'window-open':
          return Icons.blinds;
        case 'window-closed':
          return Icons.blinds_closed;
        case 'door-open':
          return Icons.door_front_door;
        case 'door-closed':
          return Icons.door_front_door;
        case 'lock':
          return Icons.lock;
        case 'lock-open':
          return Icons.lock_open;
        case 'television':
          return Icons.tv;
        case 'speaker':
          return Icons.speaker;
        case 'vacuum':
          return Icons.cleaning_services;
        case 'camera':
          return Icons.camera_alt;
        case 'weather-sunny':
          return Icons.wb_sunny;
        case 'weather-cloudy':
          return Icons.cloud;
        case 'weather-rainy':
          return Icons.water;
        case 'weather-snowy':
          return Icons.ac_unit;
        case 'weather-windy':
          return Icons.air;
        default:
          break;
      }
    }

    // 根据实体类型返回默认图标
    switch (entity.domain) {
      case 'light':
        return entity.isOn ? Icons.lightbulb : Icons.lightbulb_outline;
      case 'switch':
        return entity.isOn ? Icons.toggle_on : Icons.toggle_off;
      case 'fan':
        return Icons.air;
      case 'climate':
        return Icons.thermostat;
      case 'sensor':
        if (entity.deviceClass == 'temperature') {
          return Icons.thermostat;
        } else if (entity.deviceClass == 'humidity') {
          return Icons.water_drop;
        } else if (entity.deviceClass == 'pressure') {
          return Icons.speed;
        } else if (entity.deviceClass == 'power') {
          return Icons.power;
        } else if (entity.deviceClass == 'battery') {
          return Icons.battery_full;
        } else {
          return Icons.sensors;
        }
      case 'binary_sensor':
        if (entity.deviceClass == 'door') {
          return entity.isOn ? Icons.door_front_door : Icons.door_front_door;
        } else if (entity.deviceClass == 'window') {
          return entity.isOn ? Icons.blinds : Icons.blinds_closed;
        } else if (entity.deviceClass == 'motion') {
          return Icons.motion_photos_on;
        } else if (entity.deviceClass == 'presence') {
          return Icons.person;
        } else {
          return Icons.sensors;
        }
      case 'cover':
        return entity.isOn ? Icons.blinds : Icons.blinds_closed;
      case 'lock':
        return entity.isOn ? Icons.lock_open : Icons.lock;
      case 'media_player':
        return Icons.tv;
      case 'camera':
        return Icons.camera_alt;
      case 'vacuum':
        return Icons.cleaning_services;
      case 'scene':
        return Icons.movie;
      case 'script':
        return Icons.code;
      case 'automation':
        return Icons.auto_fix_high;
      case 'weather':
        return Icons.wb_sunny;
      case 'sun':
        return Icons.wb_sunny;
      case 'device_tracker':
        return Icons.location_on;
      case 'person':
        return Icons.person;
      default:
        return Icons.devices;
    }
  }

  /// 获取图标背景颜色
  Color _getIconBackgroundColor(BuildContext context) {
    if (entity.isUnavailable) {
      return Theme.of(context).colorScheme.surfaceVariant;
    }

    if (entity.isOn) {
      return Theme.of(context).colorScheme.primary.withOpacity(0.2);
    }

    return Theme.of(context).colorScheme.surfaceVariant;
  }

  /// 获取图标颜色
  Color _getIconColor(BuildContext context) {
    if (entity.isUnavailable) {
      return Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5);
    }

    if (entity.isOn) {
      return Theme.of(context).colorScheme.primary;
    }

    return Theme.of(context).colorScheme.onSurfaceVariant;
  }

  /// 获取状态文本
  String _getStateText() {
    if (entity.isUnavailable) {
      return '不可用';
    }

    if (entity.isUnknown) {
      return '未知';
    }

    // 对于传感器，显示状态值和单位
    if (entity.isSensor) {
      if (entity.unitOfMeasurement != null) {
        return '${entity.state} ${entity.unitOfMeasurement}';
      }
      return entity.state;
    }

    // 对于二进制传感器，根据设备类型显示状态
    if (entity.isBinarySensor) {
      switch (entity.deviceClass) {
        case 'door':
          return entity.isOn ? '开启' : '关闭';
        case 'window':
          return entity.isOn ? '开启' : '关闭';
        case 'motion':
          return entity.isOn ? '有动作' : '无动作';
        case 'presence':
          return entity.isOn ? '在家' : '离家';
        case 'connectivity':
          return entity.isOn ? '已连接' : '未连接';
        default:
          return entity.isOn ? '开启' : '关闭';
      }
    }

    // 对于开关和灯，显示开/关状态
    if (entity.isSwitch || entity.isLight) {
      return entity.isOn ? '开启' : '关闭';
    }

    // 对于风扇，显示开/关状态
    if (entity.isFan) {
      return entity.isOn ? '开启' : '关闭';
    }

    // 对于恒温器，显示当前温度
    if (entity.isClimate) {
      final currentTemp = entity.attributes['current_temperature'];
      final targetTemp = entity.attributes['temperature'];
      if (currentTemp != null && targetTemp != null) {
        return '当前: $currentTemp°C, 目标: $targetTemp°C';
      } else if (currentTemp != null) {
        return '当前: $currentTemp°C';
      }
      return entity.state;
    }

    // 对于锁，显示锁定/解锁状态
    if (entity.isLock) {
      return entity.isOn ? '解锁' : '锁定';
    }

    // 对于窗帘，显示开/关状态
    if (entity.isCover) {
      return entity.isOn ? '开启' : '关闭';
    }

    // 对于媒体播放器，显示播放状态
    if (entity.isMediaPlayer) {
      switch (entity.state) {
        case 'playing':
          return '播放中';
        case 'paused':
          return '已暂停';
        case 'idle':
          return '空闲';
        case 'standby':
          return '待机';
        case 'off':
          return '关闭';
        default:
          return entity.state;
      }
    }

    return entity.state;
  }

  /// 获取状态颜色
  Color _getStateColor(BuildContext context) {
    if (entity.isUnavailable) {
      return Theme.of(context).colorScheme.error;
    }

    if (entity.isUnknown) {
      return Theme.of(context).colorScheme.onSurfaceVariant;
    }

    if (entity.isOn) {
      return Theme.of(context).colorScheme.primary;
    }

    return Theme.of(context).colorScheme.onSurfaceVariant;
  }
}
