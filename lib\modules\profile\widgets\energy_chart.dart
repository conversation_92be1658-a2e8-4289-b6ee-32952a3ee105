import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../app/services/statistics_service.dart';

/// 能源图表组件
class EnergyChart extends StatelessWidget {
  /// 能源数据
  final List<EnergyConsumptionData> data;
  
  /// 能源周期
  final EnergyPeriod period;
  
  const EnergyChart({
    Key? key,
    required this.data,
    required this.period,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Text('暂无数据'),
      );
    }
    
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxY(),
        barTouchData: BarTouchData(
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                '${data[groupIndex].date}\n',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                children: [
                  TextSpan(
                    text: '${rod.toY.toStringAsFixed(2)} kWh',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value < 0 || value >= data.length) {
                  return const SizedBox.shrink();
                }
                
                // 根据周期显示不同的日期格式
                String title = data[value.toInt()].date;
                
                // 对于周和月，可能需要截断或缩写
                if (period == EnergyPeriod.weekly || period == EnergyPeriod.monthly) {
                  // 简化显示，例如只显示月份的第一部分
                  final parts = title.split('-');
                  if (parts.length > 1) {
                    title = parts[1];
                  }
                }
                
                return Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 12,
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: _getBarGroups(),
        gridData: FlGridData(
          show: true,
          horizontalInterval: _getMaxY() / 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withOpacity(0.2),
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }
  
  /// 获取最大Y值
  double _getMaxY() {
    if (data.isEmpty) return 10;
    
    double maxY = 0;
    for (final item in data) {
      if (item.totalEnergy > maxY) {
        maxY = item.totalEnergy;
      }
    }
    
    // 增加一点余量
    return (maxY * 1.2).ceilToDouble();
  }
  
  /// 获取柱状图组
  List<BarChartGroupData> _getBarGroups() {
    return List.generate(data.length, (index) {
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: data[index].totalEnergy,
            color: _getBarColor(index),
            width: 16,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      );
    });
  }
  
  /// 获取柱状图颜色
  Color _getBarColor(int index) {
    // 根据周期和索引生成不同的颜色
    switch (period) {
      case EnergyPeriod.daily:
        return Colors.blue.shade300;
      case EnergyPeriod.weekly:
        return Colors.green.shade300;
      case EnergyPeriod.monthly:
        return Colors.orange.shade300;
    }
  }
}
