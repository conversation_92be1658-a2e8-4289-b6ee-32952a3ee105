{"buildFiles": ["D:\\software\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\5wp2s226\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\5wp2s226\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\software\\android-sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\software\\android-sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}