import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/data/models/entity_model.dart';
import '../../../../app/services/ha_service.dart';
import 'base_card.dart';

/// 灯光卡片
class LightCard extends StatelessWidget {
  /// 卡片ID
  final String id;

  /// 实体ID
  final String entityId;

  /// 卡片样式
  final CardStyle style;

  /// 卡片尺寸
  final CardSize size;

  /// 是否处于编辑模式
  final bool editMode;

  /// 自定义标题
  final String? customTitle;

  /// 是否显示标题栏
  final bool showTitle;

  /// 删除事件
  final VoidCallback? onDelete;

  /// 编辑事件
  final VoidCallback? onEdit;

  const LightCard({
    super.key,
    required this.id,
    required this.entityId,
    this.style = CardStyle.defaultStyle,
    this.size = CardSize.medium,
    this.editMode = false,
    this.customTitle,
    this.showTitle = true,
    this.onDelete,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final haService = Get.find<HaService>();

    return Obx(() {
      final entity = haService.getEntity(entityId);

      if (entity == null) {
        return _buildErrorCard(context);
      }

      return BaseCard(
        id: id,
        title: entity.friendlyName,
        customTitle: customTitle,
        showTitle: showTitle,
        icon: Icons.lightbulb,
        style: style,
        size: size,
        editMode: editMode,
        onDelete: onDelete,
        onEdit: onEdit,
        onTap: () => _toggleLight(entity),
        content: _buildLightContent(context, entity),
      );
    });
  }

  /// 构建灯光内容
  Widget _buildLightContent(BuildContext context, EntityModel entity) {
    final isOn = entity.isOn;
    final hasBrightness = entity.supportsBrightness;
    final brightness = entity.attributes['brightness'] as int? ?? 0;
    final brightnessPercentage = (brightness / 255 * 100).round();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 灯光图标
        Icon(
          isOn ? Icons.lightbulb : Icons.lightbulb_outline,
          size: 48,
          color: isOn ? _getLightColor(entity) : Colors.grey,
        ),

        const SizedBox(height: 16),

        // 状态文本
        Text(
          isOn ? '开启' : '关闭',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isOn ? _getLightColor(entity) : Colors.grey,
          ),
        ),

        // 亮度信息（如果支持）
        if (isOn && hasBrightness) ...[
          const SizedBox(height: 8),
          Text(
            '亮度: $brightnessPercentage%',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),

          const SizedBox(height: 8),

          // 亮度滑块
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 4,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
              valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
              valueIndicatorTextStyle: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
            child: Slider(
              value: brightness.toDouble(),
              min: 0,
              max: 255,
              divisions: 25,
              label: brightnessPercentage.toString(),
              onChanged: (value) => _setBrightness(entity, value.toInt()),
              activeColor: _getLightColor(entity),
              inactiveColor: _getLightColor(entity).withAlpha(77),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建错误卡片
  Widget _buildErrorCard(BuildContext context) {
    return BaseCard(
      id: id,
      title: '未找到设备',
      customTitle: customTitle,
      showTitle: showTitle,
      icon: Icons.error,
      style: style,
      size: size,
      editMode: editMode,
      onDelete: onDelete,
      onEdit: onEdit,
      content: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              '设备不可用',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换灯光状态
  void _toggleLight(EntityModel entity) {
    final haService = Get.find<HaService>();

    if (entity.isOn) {
      haService.turnOff(entity.entityId);
    } else {
      haService.turnOn(entity.entityId);
    }
  }

  /// 设置亮度
  void _setBrightness(EntityModel entity, int brightness) {
    final haService = Get.find<HaService>();

    haService.turnOn(entity.entityId, data: {
      'brightness': brightness,
    });
  }

  /// 获取灯光颜色
  Color _getLightColor(EntityModel entity) {
    // 如果支持RGB颜色
    if (entity.supportsColor && entity.attributes.containsKey('rgb_color')) {
      final rgbColor = entity.attributes['rgb_color'] as List<dynamic>;
      if (rgbColor.length >= 3) {
        return Color.fromRGBO(
          rgbColor[0] as int,
          rgbColor[1] as int,
          rgbColor[2] as int,
          1.0,
        );
      }
    }

    // 默认颜色
    return Colors.amber;
  }
}
