import 'dart:convert';

/// Home Assistant实体模型
class EntityModel {
  final String entityId;
  final String state;
  final Map<String, dynamic> attributes;
  final String lastChanged;
  final String lastUpdated;
  final String? context;
  
  // 实体类型（从entity_id中提取）
  String get domain => entityId.split('.').first;
  
  // 实体ID（从entity_id中提取）
  String get id => entityId.split('.').last;
  
  // 友好名称（从attributes中获取）
  String get friendlyName => attributes['friendly_name'] ?? id;
  
  // 图标（从attributes中获取）
  String? get icon => attributes['icon'];
  
  // 设备类（从attributes中获取）
  String? get deviceClass => attributes['device_class'];
  
  // 单位（从attributes中获取）
  String? get unitOfMeasurement => attributes['unit_of_measurement'];
  
  // 是否可用
  bool get available => attributes['available'] != false;
  
  // 是否支持开关操作
  bool get supportsTurnOn => domain == 'light' || 
                            domain == 'switch' || 
                            domain == 'fan' || 
                            domain == 'automation' || 
                            domain == 'script' || 
                            domain == 'scene';
  
  // 是否支持亮度调节
  bool get supportsBrightness => domain == 'light' && 
                                attributes.containsKey('brightness');
  
  // 是否支持颜色调节
  bool get supportsColor => domain == 'light' && 
                          (attributes.containsKey('rgb_color') || 
                           attributes.containsKey('hs_color') || 
                           attributes.containsKey('xy_color'));
  
  // 是否支持色温调节
  bool get supportsColorTemp => domain == 'light' && 
                              attributes.containsKey('color_temp');
  
  // 是否支持风扇速度调节
  bool get supportsFanSpeed => domain == 'fan' && 
                              attributes.containsKey('percentage');
  
  // 是否支持温度调节
  bool get supportsTemperature => domain == 'climate' && 
                                attributes.containsKey('temperature');
  
  // 是否支持湿度调节
  bool get supportsHumidity => domain == 'humidifier' && 
                              attributes.containsKey('humidity');
  
  // 是否支持锁定/解锁
  bool get supportsLock => domain == 'lock';
  
  // 是否支持打开/关闭
  bool get supportsCover => domain == 'cover';
  
  // 是否支持媒体播放
  bool get supportsMedia => domain == 'media_player';
  
  // 是否是传感器
  bool get isSensor => domain == 'sensor' || domain == 'binary_sensor';
  
  // 是否是二进制传感器
  bool get isBinarySensor => domain == 'binary_sensor';
  
  // 是否是开关
  bool get isSwitch => domain == 'switch';
  
  // 是否是灯
  bool get isLight => domain == 'light';
  
  // 是否是风扇
  bool get isFan => domain == 'fan';
  
  // 是否是恒温器
  bool get isClimate => domain == 'climate';
  
  // 是否是加湿器
  bool get isHumidifier => domain == 'humidifier';
  
  // 是否是锁
  bool get isLock => domain == 'lock';
  
  // 是否是窗帘
  bool get isCover => domain == 'cover';
  
  // 是否是媒体播放器
  bool get isMediaPlayer => domain == 'media_player';
  
  // 是否是场景
  bool get isScene => domain == 'scene';
  
  // 是否是脚本
  bool get isScript => domain == 'script';
  
  // 是否是自动化
  bool get isAutomation => domain == 'automation';
  
  // 是否是开启状态
  bool get isOn => state == 'on' || state == 'open' || state == 'unlocked' || state == 'playing';
  
  // 是否是关闭状态
  bool get isOff => state == 'off' || state == 'closed' || state == 'locked' || state == 'paused' || state == 'idle';
  
  // 是否是不可用状态
  bool get isUnavailable => state == 'unavailable';
  
  // 是否是未知状态
  bool get isUnknown => state == 'unknown';
  
  EntityModel({
    required this.entityId,
    required this.state,
    required this.attributes,
    required this.lastChanged,
    required this.lastUpdated,
    this.context,
  });
  
  factory EntityModel.fromJson(Map<String, dynamic> json) {
    return EntityModel(
      entityId: json['entity_id'] as String,
      state: json['state'] as String,
      attributes: json['attributes'] as Map<String, dynamic>,
      lastChanged: json['last_changed'] as String,
      lastUpdated: json['last_updated'] as String,
      context: json['context'] != null ? jsonEncode(json['context']) : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'entity_id': entityId,
      'state': state,
      'attributes': attributes,
      'last_changed': lastChanged,
      'last_updated': lastUpdated,
      'context': context != null ? jsonDecode(context!) : null,
    };
  }
  
  @override
  String toString() {
    return 'EntityModel{entityId: $entityId, state: $state, friendlyName: $friendlyName}';
  }
}
