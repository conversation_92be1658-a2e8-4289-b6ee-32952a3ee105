/// 存储键定义
class StorageKeys {
  // 认证相关
  static const String serverUrl = 'server_url';
  static const String accessToken = 'access_token';
  static const String rememberMe = 'remember_me';

  // 主题相关
  static const String themeMode = 'theme_mode';

  // 设备相关
  static const String deviceGroups = 'device_groups';
  static const String favoriteDevices = 'favorite_devices';
  static const String deviceAliases = 'device_aliases';

  // 场景相关
  static const String scenes = 'scenes';
  static const String sceneGroups = 'scene_groups';
  static const String favoriteScenes = 'favorite_scenes';

  // 实体数据缓存
  static const String cachedEntities = 'cached_entities';
  static const String lastCacheTime = 'last_cache_time';

  // WebDAV相关
  static const String webdavServerUrl = 'webdav_server_url';
  static const String webdavUsername = 'webdav_username';
  static const String webdavPassword = 'webdav_password';
  static const String webdavRemotePath = 'webdav_remote_path';

  // 天气相关
  static const String weatherApiKey = 'weather_api_key';
  static const String weatherLocation = 'weather_location';

  // 统计相关
  static const String energyStatistics = 'energy_statistics';
  static const String deviceUsageStatistics = 'device_usage_statistics';

  // UI设置相关
  static const String cardStyle = 'card_style';
  static const String homeLayout = 'home_layout';
}
