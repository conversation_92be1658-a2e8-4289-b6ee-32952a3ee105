{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/software/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/software/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/software/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/software/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-2efc7be8a90690f276c0.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-567fd5471e59065c0fd0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-732f769cbde9c44f10f3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-567fd5471e59065c0fd0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-732f769cbde9c44f10f3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-2efc7be8a90690f276c0.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}