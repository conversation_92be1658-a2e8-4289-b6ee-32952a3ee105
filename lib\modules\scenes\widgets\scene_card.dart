import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/models/scene_model.dart';
import '../controllers/scenes_controller.dart';

/// 场景卡片组件
class SceneCard extends StatelessWidget {
  final SceneModel scene;
  final Function()? onTap;
  final Function()? onEdit;
  final Function()? onDelete;
  
  const SceneCard({
    Key? key,
    required this.scene,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ScenesController>();
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 场景图标
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: scene.color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getIconData(scene.icon),
                      color: scene.color,
                      size: 24,
                    ),
                  ),
                  
                  // 操作按钮
                  Row(
                    children: [
                      // 收藏按钮
                      Obx(() => IconButton(
                        icon: Icon(
                          controller.isFavorite(scene.id)
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: controller.isFavorite(scene.id)
                              ? Colors.red
                              : Colors.grey,
                        ),
                        onPressed: () => controller.toggleFavorite(scene.id),
                        tooltip: controller.isFavorite(scene.id) ? '取消收藏' : '收藏',
                      )),
                      
                      // 编辑按钮（仅自定义场景可编辑）
                      if (scene.isCustom && onEdit != null)
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue),
                          onPressed: onEdit,
                          tooltip: '编辑',
                        ),
                      
                      // 删除按钮（仅自定义场景可删除）
                      if (scene.isCustom && onDelete != null)
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: onDelete,
                          tooltip: '删除',
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // 场景名称
              Text(
                scene.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 8),
              
              // 场景类型标签
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: scene.isCustom ? Colors.green.withOpacity(0.2) : Colors.blue.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      scene.isCustom ? '自定义场景' : 'HA场景',
                      style: TextStyle(
                        fontSize: 12,
                        color: scene.isCustom ? Colors.green : Colors.blue,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // 设备数量
                  Text(
                    '${scene.entityIds.length}个设备',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 获取图标数据
  IconData _getIconData(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return Icons.auto_awesome;
    }
    
    // 常用图标映射
    final iconMap = {
      'home': Icons.home,
      'lightbulb': Icons.lightbulb,
      'tv': Icons.tv,
      'bed': Icons.bed,
      'movie': Icons.movie,
      'music_note': Icons.music_note,
      'restaurant': Icons.restaurant,
      'local_cafe': Icons.local_cafe,
      'wb_sunny': Icons.wb_sunny,
      'nightlight': Icons.nightlight,
      'auto_awesome': Icons.auto_awesome,
      'celebration': Icons.celebration,
      'weekend': Icons.weekend,
      'sports_esports': Icons.sports_esports,
      'fitness_center': Icons.fitness_center,
      'spa': Icons.spa,
      'pool': Icons.pool,
      'shower': Icons.shower,
      'local_bar': Icons.local_bar,
      'local_dining': Icons.local_dining,
    };
    
    return iconMap[iconName] ?? Icons.auto_awesome;
  }
}
