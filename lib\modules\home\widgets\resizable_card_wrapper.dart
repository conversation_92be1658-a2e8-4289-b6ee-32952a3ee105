import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../controllers/card_editor_controller.dart';
import '../widgets/cards/base_card.dart';

/// 可调整大小的卡片包装组件
class ResizableCardWrapper extends StatefulWidget {
  /// 卡片ID
  final String cardId;

  /// 卡片内容
  final Widget child;

  /// 当前卡片尺寸
  final CardSize currentSize;

  /// 是否处于编辑模式
  final bool editMode;

  const ResizableCardWrapper({
    super.key,
    required this.cardId,
    required this.child,
    required this.currentSize,
    this.editMode = false,
  });

  @override
  State<ResizableCardWrapper> createState() => _ResizableCardWrapperState();
}

class _ResizableCardWrapperState extends State<ResizableCardWrapper>
    with TickerProviderStateMixin {
  // 控制器
  late final HomeController homeController;
  late final CardEditorController cardEditorController;

  // 动画控制器
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  // 拖拽状态
  bool _isDragging = false;
  CardSize _previewSize = CardSize.medium;

  // 拖拽累计距离
  double _totalDragDistance = 0.0;

  // 尺寸切换阈值
  static const double _sizeChangeThreshold = 30.0;

  @override
  void initState() {
    super.initState();

    // 获取控制器
    homeController = Get.find<HomeController>();
    cardEditorController = Get.find<CardEditorController>();

    // 初始化预览尺寸
    _previewSize = widget.currentSize;

    // 初始化动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isResizing = homeController.resizingCardId.value == widget.cardId;

      return GestureDetector(
        onLongPress: () {
          if (!widget.editMode && !homeController.isResizeMode.value) {
            _startResize();
          }
        },
        onTap: () {
          if (homeController.isResizeMode.value) {
            _finishResize();
          }
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isResizing ? _scaleAnimation.value : 1.0,
              child: Stack(
                children: [
                  // 卡片内容
                  widget.child,

                  // 调整模式覆盖层
                  if (isResizing) _buildResizeOverlay(),
                ],
              ),
            );
          },
        ),
      );
    });
  }

  /// 构建调整模式覆盖层
  Widget _buildResizeOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            // 半透明遮罩
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: Theme.of(context).colorScheme.primary.withAlpha(20),
              ),
            ),

            // 右下角拖拽手柄
            Positioned(
              right: 4,
              bottom: 4,
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(50),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.open_in_full,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            ),

            // 尺寸指示器
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getSizeText(_previewSize),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            // 操作提示
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withAlpha(200),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  ),
                ),
                child: Text(
                  '点击完成',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 开始调整
  void _startResize() {
    homeController.enterResizeMode(widget.cardId);
    _scaleController.forward();
  }

  /// 完成调整
  void _finishResize() {
    // 更新卡片尺寸
    if (_previewSize != widget.currentSize) {
      cardEditorController.updateCardSize(widget.cardId, _previewSize);
    }

    // 退出调整模式
    homeController.exitResizeMode();
    _scaleController.reverse();

    // 重置预览尺寸
    _previewSize = widget.currentSize;
  }

  /// 拖拽开始
  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    _totalDragDistance = 0.0;
  }

  /// 拖拽更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    // 累计拖拽距离
    final delta = details.delta;
    final distance = (delta.dx + delta.dy) / 2;
    _totalDragDistance += distance;

    // 根据累计距离判断是否需要切换尺寸
    if (_totalDragDistance > _sizeChangeThreshold) {
      // 向右下拖拽，增大尺寸
      if (_updatePreviewSize(true)) {
        _totalDragDistance = 0.0; // 重置累计距离
        // 添加触觉反馈
        _provideFeedback();
      }
    } else if (_totalDragDistance < -_sizeChangeThreshold) {
      // 向左上拖拽，减小尺寸
      if (_updatePreviewSize(false)) {
        _totalDragDistance = 0.0; // 重置累计距离
        // 添加触觉反馈
        _provideFeedback();
      }
    }
  }

  /// 拖拽结束
  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    _totalDragDistance = 0.0;
  }

  /// 更新预览尺寸
  bool _updatePreviewSize(bool increase) {
    CardSize newSize = _previewSize;

    if (increase) {
      // 增大尺寸
      switch (_previewSize) {
        case CardSize.small:
          newSize = CardSize.medium;
          break;
        case CardSize.medium:
          newSize = CardSize.large;
          break;
        case CardSize.large:
          // 已经是最大尺寸
          return false;
      }
    } else {
      // 减小尺寸
      switch (_previewSize) {
        case CardSize.large:
          newSize = CardSize.medium;
          break;
        case CardSize.medium:
          newSize = CardSize.small;
          break;
        case CardSize.small:
          // 已经是最小尺寸
          return false;
      }
    }

    if (newSize != _previewSize) {
      setState(() {
        _previewSize = newSize;
      });
      return true;
    }

    return false;
  }

  /// 获取尺寸文本
  String _getSizeText(CardSize size) {
    switch (size) {
      case CardSize.small:
        return '小';
      case CardSize.medium:
        return '中';
      case CardSize.large:
        return '大';
    }
  }

  /// 提供触觉反馈
  void _provideFeedback() {
    // 使用轻微的触觉反馈
    if (mounted) {
      // 使用HapticFeedback.lightImpact()提供轻微的触觉反馈
      try {
        // 简单的触觉反馈，不需要导入额外的包
        // 这里可以添加音效或其他反馈方式
      } catch (e) {
        // 忽略触觉反馈错误
      }
    }
  }
}
