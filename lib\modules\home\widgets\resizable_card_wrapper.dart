import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../controllers/card_editor_controller.dart';
import '../widgets/cards/base_card.dart';

/// 可调整大小的卡片包装组件
class ResizableCardWrapper extends StatefulWidget {
  /// 卡片ID
  final String cardId;

  /// 卡片内容
  final Widget child;

  /// 当前卡片尺寸
  final CardSize currentSize;

  /// 是否处于编辑模式
  final bool editMode;

  const ResizableCardWrapper({
    super.key,
    required this.cardId,
    required this.child,
    required this.currentSize,
    this.editMode = false,
  });

  @override
  State<ResizableCardWrapper> createState() => _ResizableCardWrapperState();
}

class _ResizableCardWrapperState extends State<ResizableCardWrapper>
    with TickerProviderStateMixin {
  // 控制器
  late final HomeController homeController;
  late final CardEditorController cardEditorController;

  // 动画控制器
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  // 拖拽状态
  bool _isDragging = false;
  CardSize _previewSize = CardSize.medium;

  // 拖拽起始位置
  Offset? _dragStartPosition;

  // 拖拽当前位置
  Offset _currentDragPosition = Offset.zero;

  // 尺寸切换阈值
  static const double _sizeChangeThreshold = 40.0;

  @override
  void initState() {
    super.initState();

    // 获取控制器
    homeController = Get.find<HomeController>();
    cardEditorController = Get.find<CardEditorController>();

    // 初始化预览尺寸
    _previewSize = widget.currentSize;

    // 初始化动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isResizing = homeController.resizingCardId.value == widget.cardId;

      return GestureDetector(
        onLongPress: () {
          if (!widget.editMode && !homeController.isResizeMode.value) {
            _startResize();
          }
        },
        onTap: () {
          if (homeController.isResizeMode.value) {
            _finishResize();
          }
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: isResizing ? _scaleAnimation.value : 1.0,
              child: Stack(
                children: [
                  // 卡片内容 - 在调整模式下使用预览尺寸
                  isResizing ? _buildPreviewCard() : widget.child,

                  // 调整模式覆盖层
                  if (isResizing) _buildResizeOverlay(),
                ],
              ),
            );
          },
        ),
      );
    });
  }

  /// 构建预览卡片（使用预览尺寸）
  Widget _buildPreviewCard() {
    // 直接使用原始卡片内容，只改变容器尺寸，避免重新请求数据
    final aspectRatio = _getAspectRatioForSize(_previewSize);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withAlpha(150),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withAlpha(50),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(14),
            // 直接使用原始卡片，不重新构建，避免重新请求数据
            child: widget.child,
          ),
        ),
      ),
    );
  }

  /// 获取指定尺寸的宽高比
  double _getAspectRatioForSize(CardSize size) {
    switch (size) {
      case CardSize.small:
        return 1.0; // 1:1
      case CardSize.medium:
        return 2.0; // 2:1
      case CardSize.large:
        return 1.0; // 2:2 (显示为1:1，但占用2x2网格)
    }
  }

  /// 构建调整模式覆盖层
  Widget _buildResizeOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            // 半透明遮罩
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: Theme.of(context).colorScheme.primary.withAlpha(20),
              ),
            ),

            // 右下角拖拽手柄
            Positioned(
              right: 4,
              bottom: 4,
              child: GestureDetector(
                onPanStart: _onPanStart,
                onPanUpdate: _onPanUpdate,
                onPanEnd: _onPanEnd,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 150),
                  width: _isDragging ? 28 : 24,
                  height: _isDragging ? 28 : 24,
                  decoration: BoxDecoration(
                    color: _isDragging
                        ? Theme.of(context).colorScheme.primary.withAlpha(230)
                        : Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(_isDragging ? 14 : 12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(_isDragging ? 80 : 50),
                        blurRadius: _isDragging ? 6 : 4,
                        offset: Offset(0, _isDragging ? 3 : 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.open_in_full,
                    size: _isDragging ? 18 : 16,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ),
            ),

            // 尺寸指示器
            Positioned(
              top: 8,
              left: 8,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _isDragging
                      ? Theme.of(context).colorScheme.secondary
                      : Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: _isDragging ? [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.secondary.withAlpha(100),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_isDragging) ...[
                      Icon(
                        Icons.drag_indicator,
                        size: 12,
                        color: Theme.of(context).colorScheme.onSecondary,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      _getSizeText(_previewSize),
                      style: TextStyle(
                        color: _isDragging
                            ? Theme.of(context).colorScheme.onSecondary
                            : Theme.of(context).colorScheme.onPrimary,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 操作提示
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withAlpha(200),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  ),
                ),
                child: Text(
                  '点击完成',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 开始调整
  void _startResize() {
    homeController.enterResizeMode(widget.cardId);
    _scaleController.forward();

    // 进入调整模式时提供触觉反馈
    _provideFeedback();
  }

  /// 完成调整
  void _finishResize() {
    // 更新卡片尺寸
    if (_previewSize != widget.currentSize) {
      cardEditorController.updateCardSize(widget.cardId, _previewSize);
    }

    // 退出调整模式
    homeController.exitResizeMode();
    _scaleController.reverse();

    // 重置预览尺寸
    _previewSize = widget.currentSize;
  }

  /// 拖拽开始
  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    _dragStartPosition = details.localPosition;
    _currentDragPosition = details.localPosition;
  }

  /// 拖拽更新
  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging || _dragStartPosition == null) return;

    _currentDragPosition = details.localPosition;

    // 计算从起始位置的总偏移量
    final totalOffset = _currentDragPosition - _dragStartPosition!;
    final totalDistance = (totalOffset.dx + totalOffset.dy) / 2;

    // 根据总距离判断目标尺寸
    CardSize targetSize;
    if (totalDistance > _sizeChangeThreshold) {
      // 向右下拖拽，增大尺寸
      targetSize = _getNextLargerSize(widget.currentSize);
    } else if (totalDistance < -_sizeChangeThreshold) {
      // 向左上拖拽，减小尺寸
      targetSize = _getNextSmallerSize(widget.currentSize);
    } else {
      // 距离不够，保持原尺寸
      targetSize = widget.currentSize;
    }

    // 更新预览尺寸
    if (targetSize != _previewSize) {
      setState(() {
        _previewSize = targetSize;
      });
      _provideFeedback();
    }
  }

  /// 拖拽结束
  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    _dragStartPosition = null;
    _currentDragPosition = Offset.zero;
  }

  /// 获取下一个更大的尺寸
  CardSize _getNextLargerSize(CardSize currentSize) {
    switch (currentSize) {
      case CardSize.small:
        return CardSize.medium;
      case CardSize.medium:
        return CardSize.large;
      case CardSize.large:
        return CardSize.large; // 已经是最大尺寸
    }
  }

  /// 获取下一个更小的尺寸
  CardSize _getNextSmallerSize(CardSize currentSize) {
    switch (currentSize) {
      case CardSize.large:
        return CardSize.medium;
      case CardSize.medium:
        return CardSize.small;
      case CardSize.small:
        return CardSize.small; // 已经是最小尺寸
    }
  }

  /// 获取尺寸文本
  String _getSizeText(CardSize size) {
    switch (size) {
      case CardSize.small:
        return '小';
      case CardSize.medium:
        return '中';
      case CardSize.large:
        return '大';
    }
  }

  /// 提供触觉反馈
  void _provideFeedback() {
    // 使用轻微的触觉反馈
    if (mounted) {
      try {
        // 使用轻微的触觉反馈，模拟米家APP的交互体验
        HapticFeedback.lightImpact();
      } catch (e) {
        // 忽略触觉反馈错误，某些设备可能不支持
      }
    }
  }
}
