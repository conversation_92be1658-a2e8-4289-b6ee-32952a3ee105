                        -HD:\software\flutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\software\android-sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\software\android-sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\software\android-sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\software\android-sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\project\appProject\ha_smart_home\build\app\intermediates\cxx\Debug\5wp2s226\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\project\appProject\ha_smart_home\build\app\intermediates\cxx\Debug\5wp2s226\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\project\appProject\ha_smart_home\android\app\.cxx\Debug\5wp2s226\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2