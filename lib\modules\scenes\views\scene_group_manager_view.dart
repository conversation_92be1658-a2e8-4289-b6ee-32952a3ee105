import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/scenes_controller.dart';
import '../../../app/data/models/scene_group_model.dart';
import '../../../app/data/repositories/scene_repository.dart';
import '../../../shared/widgets/loading_indicator.dart';

/// 场景分组管理页面
class SceneGroupManagerView extends GetView<ScenesController> {
  const SceneGroupManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('场景分组管理'),
        actions: [
          // 添加分组按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateGroupDialog(context),
            tooltip: '添加分组',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: LoadingIndicator());
        }

        if (controller.sceneGroups.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.folder_off,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  '暂无场景分组',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('创建分组'),
                  onPressed: () => _showCreateGroupDialog(context),
                ),
              ],
            ),
          );
        }

        return ReorderableListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: controller.sceneGroups.length,
          onReorder: _handleReorder,
          itemBuilder: (context, index) {
            final group = controller.sceneGroups[index];
            return _buildGroupItem(context, group, index);
          },
        );
      }),
    );
  }

  /// 构建分组项
  Widget _buildGroupItem(BuildContext context, SceneGroupModel group, int index) {
    return Card(
      key: ValueKey(group.id),
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: Icon(
          _getIconData(group.icon ?? 'folder'),
          color: Theme.of(context).primaryColor,
        ),
        title: Text(group.name),
        subtitle: Text('${controller.getScenesInGroup(group.id).length}个场景'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 编辑按钮
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue),
              onPressed: () => _showEditGroupDialog(context, group),
              tooltip: '编辑',
            ),
            // 删除按钮
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmDialog(context, group),
              tooltip: '删除',
            ),
          ],
        ),
      ),
    );
  }

  /// 处理分组重新排序
  void _handleReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    final groups = List<SceneGroupModel>.from(controller.sceneGroups);
    final item = groups.removeAt(oldIndex);
    groups.insert(newIndex, item);

    // 更新顺序
    for (int i = 0; i < groups.length; i++) {
      final group = groups[i];
      final updatedGroup = group.copyWith(order: i);
      Get.find<SceneRepository>().saveSceneGroup(updatedGroup);
    }

    // 重新加载分组
    controller.loadSceneGroups();
  }

  /// 显示创建分组对话框
  void _showCreateGroupDialog(BuildContext context) {
    final nameController = TextEditingController();
    String? selectedIcon = 'folder';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建分组'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 分组名称
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '分组名称',
                hintText: '输入分组名称',
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),

            // 分组图标
            InkWell(
              onTap: () async {
                // 显示图标选择器
                final iconName = await _showIconSelector(context);
                if (iconName != null) {
                  selectedIcon = iconName;
                  // 刷新对话框
                  Navigator.pop(context);
                  _showCreateGroupDialog(context);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.5)),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getIconData(selectedIcon!),
                    ),
                    const SizedBox(width: 8),
                    const Text('选择图标'),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  '错误',
                  '分组名称不能为空',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // 创建分组
              final group = SceneGroupModel(
                id: 'group_${DateTime.now().millisecondsSinceEpoch}',
                name: nameController.text.trim(),
                icon: selectedIcon,
                order: controller.sceneGroups.length,
              );

              final success = await Get.find<SceneRepository>().saveSceneGroup(group);

              if (success) {
                // 重新加载分组
                controller.loadSceneGroups();

                Navigator.pop(context);
                Get.snackbar(
                  '成功',
                  '分组创建成功',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '错误',
                  '分组创建失败',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }

  /// 显示编辑分组对话框
  void _showEditGroupDialog(BuildContext context, SceneGroupModel group) {
    final nameController = TextEditingController(text: group.name);
    String? selectedIcon = group.icon;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑分组'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 分组名称
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '分组名称',
                hintText: '输入分组名称',
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),

            // 分组图标
            InkWell(
              onTap: () async {
                // 显示图标选择器
                final iconName = await _showIconSelector(context);
                if (iconName != null) {
                  selectedIcon = iconName;
                  // 刷新对话框
                  Navigator.pop(context);
                  _showEditGroupDialog(context, group);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.5)),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getIconData(selectedIcon ?? 'folder'),
                    ),
                    const SizedBox(width: 8),
                    const Text('选择图标'),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  '错误',
                  '分组名称不能为空',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // 更新分组
              final updatedGroup = group.copyWith(
                name: nameController.text.trim(),
                icon: selectedIcon,
              );

              final success = await Get.find<SceneRepository>().saveSceneGroup(updatedGroup);

              if (success) {
                // 重新加载分组
                controller.loadSceneGroups();

                Navigator.pop(context);
                Get.snackbar(
                  '成功',
                  '分组更新成功',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '错误',
                  '分组更新失败',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, SceneGroupModel group) {
    // 获取分组中的场景数量
    final scenesCount = controller.getScenesInGroup(group.id).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除分组'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('确定要删除分组"${group.name}"吗？'),
            const SizedBox(height: 8),
            if (scenesCount > 0)
              Text(
                '该分组下有$scenesCount个场景，删除分组后，这些场景将变为未分组状态。',
                style: const TextStyle(color: Colors.orange),
              ),
            const SizedBox(height: 8),
            const Text('此操作不可撤销。'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              // 删除分组
              final success = await Get.find<SceneRepository>().deleteSceneGroup(group.id);

              if (success) {
                // 重新加载分组
                controller.loadSceneGroups();

                Get.snackbar(
                  '成功',
                  '分组已删除',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '错误',
                  '删除分组失败',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 显示图标选择器
  Future<String?> _showIconSelector(BuildContext context) {
    // 常用图标列表
    final iconList = [
      'folder',
      'home',
      'lightbulb',
      'tv',
      'bed',
      'movie',
      'music_note',
      'restaurant',
      'local_cafe',
      'wb_sunny',
      'nightlight',
      'celebration',
      'weekend',
      'sports_esports',
      'fitness_center',
      'spa',
      'pool',
      'shower',
      'local_bar',
      'local_dining',
    ];

    return showDialog<String?>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择图标'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: iconList.length,
            itemBuilder: (context, index) {
              final iconName = iconList[index];
              return InkWell(
                onTap: () {
                  Navigator.pop(context, iconName);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(_getIconData(iconName)),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 获取图标数据
  IconData _getIconData(String iconName) {
    // 常用图标映射
    final iconMap = {
      'home': Icons.home,
      'lightbulb': Icons.lightbulb,
      'tv': Icons.tv,
      'bed': Icons.bed,
      'movie': Icons.movie,
      'music_note': Icons.music_note,
      'restaurant': Icons.restaurant,
      'local_cafe': Icons.local_cafe,
      'wb_sunny': Icons.wb_sunny,
      'nightlight': Icons.nightlight,
      'auto_awesome': Icons.auto_awesome,
      'celebration': Icons.celebration,
      'weekend': Icons.weekend,
      'sports_esports': Icons.sports_esports,
      'fitness_center': Icons.fitness_center,
      'spa': Icons.spa,
      'pool': Icons.pool,
      'shower': Icons.shower,
      'local_bar': Icons.local_bar,
      'local_dining': Icons.local_dining,
      'folder': Icons.folder,
    };

    return iconMap[iconName] ?? Icons.folder;
  }


}