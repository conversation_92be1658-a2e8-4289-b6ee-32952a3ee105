import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/services/statistics_service.dart';
import '../../../app/services/log_service.dart';

/// 统计页面控制器
class StatisticsController extends GetxController {
  // 统计服务
  final StatisticsService _statisticsService = Get.find<StatisticsService>();

  // 当前选中的能源周期
  final Rx<EnergyPeriod> selectedEnergyPeriod = EnergyPeriod.daily.obs;

  // 当前选中的设备类型
  final RxString selectedDeviceType = 'all'.obs;

  // 当前选中的房间
  final RxString selectedRoom = 'all'.obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);

  @override
  void onInit() {
    super.onInit();

    // 加载统计数据
    loadStatisticsData();
  }

  /// 加载统计数据
  Future<void> loadStatisticsData() async {
    try {
      isLoading.value = true;
      errorMessage.value = null;

      // 加载能源消耗数据
      await _statisticsService.loadEnergyConsumptionData();

      // 加载设备使用频率数据
      await _statisticsService.loadDeviceUsageData();

      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = '加载统计数据失败: $e';
      LogService.to.e('加载统计数据失败', e);
    }
  }

  /// 设置能源周期
  void setEnergyPeriod(EnergyPeriod period) {
    selectedEnergyPeriod.value = period;
  }

  /// 设置设备类型
  void setDeviceType(String type) {
    selectedDeviceType.value = type;
  }

  /// 设置房间
  void setRoom(String room) {
    selectedRoom.value = room;
  }

  /// 导出能源数据
  Future<void> exportEnergyData() async {
    try {
      await _statisticsService.exportEnergyDataToCsv(selectedEnergyPeriod.value);

      Get.snackbar(
        '导出成功',
        '能源数据已导出',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('导出能源数据失败', e);
      Get.snackbar(
        '导出失败',
        '导出能源数据失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取当前能源数据
  List<EnergyConsumptionData> get currentEnergyData {
    switch (selectedEnergyPeriod.value) {
      case EnergyPeriod.daily:
        return _statisticsService.dailyEnergyData;
      case EnergyPeriod.weekly:
        return _statisticsService.weeklyEnergyData;
      case EnergyPeriod.monthly:
        return _statisticsService.monthlyEnergyData;
    }
  }

  /// 获取过滤后的设备使用频率数据
  List<DeviceUsageData> get filteredDeviceUsageData {
    final data = _statisticsService.deviceUsageData.toList();

    // 按设备类型过滤
    if (selectedDeviceType.value != 'all') {
      return data.where((item) => item.deviceType == selectedDeviceType.value).toList();
    }

    // 按房间过滤（需要实现）
    // TODO: 实现按房间过滤

    return data;
  }

  /// 获取设备类型列表
  List<String> get deviceTypes {
    final types = <String>{'all'};

    for (final data in _statisticsService.deviceUsageData) {
      types.add(data.deviceType);
    }

    return types.toList()..sort();
  }

  /// 获取设备类型显示名称
  String getDeviceTypeDisplayName(String type) {
    switch (type) {
      case 'all':
        return '全部';
      case 'light':
        return '灯光';
      case 'switch':
        return '开关';
      case 'climate':
        return '空调';
      case 'fan':
        return '风扇';
      default:
        return type;
    }
  }
}
