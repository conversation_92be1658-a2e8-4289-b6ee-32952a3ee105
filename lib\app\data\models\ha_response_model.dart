/// Home Assistant WebSocket API响应模型
class HaResponseModel {
  final String type;
  final int? id;
  final bool? success;
  final String? error;
  final dynamic result;
  final String? message;
  final String? haVersion;
  
  HaResponseModel({
    required this.type,
    this.id,
    this.success,
    this.error,
    this.result,
    this.message,
    this.haVersion,
  });
  
  factory HaResponseModel.fromJson(Map<String, dynamic> json) {
    return HaResponseModel(
      type: json['type'] as String,
      id: json['id'] as int?,
      success: json['success'] as bool?,
      error: json['error'] as String?,
      result: json['result'],
      message: json['message'] as String?,
      haVersion: json['ha_version'] as String?,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      if (id != null) 'id': id,
      if (success != null) 'success': success,
      if (error != null) 'error': error,
      if (result != null) 'result': result,
      if (message != null) 'message': message,
      if (haVersion != null) 'ha_version': haVersion,
    };
  }
  
  @override
  String toString() {
    return 'HaResponseModel{type: $type, id: $id, success: $success, error: $error, message: $message, haVersion: $haVersion}';
  }
}
