import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/repositories/scene_repository.dart';
import '../../../app/data/models/scene_model.dart';
import '../../../app/data/models/scene_group_model.dart';
import '../../../app/data/local/storage_keys.dart';
import '../../../app/data/local/storage_service.dart';
import '../../../app/services/log_service.dart';
import '../../../app/services/websocket_service.dart';

/// 场景列表控制器
class ScenesController extends GetxController {
  // 场景仓库
  final SceneRepository _sceneRepository = Get.find<SceneRepository>();

  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();

  // 场景列表
  final RxList<SceneModel> scenes = <SceneModel>[].obs;

  // 场景分组列表
  final RxList<SceneGroupModel> sceneGroups = <SceneGroupModel>[].obs;

  // 收藏的场景
  final RxList<String> favoriteScenes = <String>[].obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);

  // 搜索关键字
  final RxString searchQuery = ''.obs;

  // 当前选中的分组ID
  final RxString selectedGroupId = 'all'.obs;

  // 是否显示收藏场景
  final RxBool showFavorites = false.obs;

  @override
  void onInit() {
    super.onInit();

    // 加载场景数据
    loadScenes();

    // 加载场景分组
    loadSceneGroups();

    // 加载收藏场景
    loadFavoriteScenes();

    // 监听WebSocket连接状态
    Get.find<WebSocketService>().isConnected.listen((connected) {
      if (connected) {
        // 连接成功后刷新场景列表
        loadScenes();
      }
    });
  }

  /// 加载场景列表
  Future<void> loadScenes() async {
    try {
      isLoading.value = true;
      errorMessage.value = null;

      // 获取所有场景
      final allScenes = await _sceneRepository.getAllScenes();

      // 更新场景列表
      scenes.value = allScenes;

      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
      errorMessage.value = '加载场景失败: $e';
      LogService.to.e('加载场景失败', e);
    }
  }

  /// 加载场景分组
  Future<void> loadSceneGroups() async {
    try {
      // 获取所有场景分组
      final groups = await _sceneRepository.getAllSceneGroups();

      // 更新分组列表
      sceneGroups.value = groups;
    } catch (e) {
      LogService.to.e('加载场景分组失败', e);
    }
  }

  /// 加载收藏场景
  void loadFavoriteScenes() {
    try {
      final favorites = _storageService.getList<String>(StorageKeys.favoriteScenes);
      if (favorites != null) {
        favoriteScenes.value = favorites;
        LogService.to.i('加载了 ${favoriteScenes.length} 个收藏场景');
      } else {
        LogService.to.d('没有保存的收藏场景');
      }
    } catch (e) {
      LogService.to.e('加载收藏场景失败', e);
    }
  }

  /// 保存收藏场景
  Future<void> _saveFavoriteScenes() async {
    try {
      await _storageService.saveList(StorageKeys.favoriteScenes, favoriteScenes);
      LogService.to.i('保存了 ${favoriteScenes.length} 个收藏场景');
    } catch (e) {
      LogService.to.e('保存收藏场景失败', e);
    }
  }

  /// 添加场景到收藏
  Future<void> addToFavorites(String sceneId) async {
    if (!favoriteScenes.contains(sceneId)) {
      favoriteScenes.add(sceneId);
      await _saveFavoriteScenes();
    }
  }

  /// 从收藏中移除场景
  Future<void> removeFromFavorites(String sceneId) async {
    if (favoriteScenes.contains(sceneId)) {
      favoriteScenes.remove(sceneId);
      await _saveFavoriteScenes();
    }
  }

  /// 切换场景收藏状态
  Future<void> toggleFavorite(String sceneId) async {
    if (favoriteScenes.contains(sceneId)) {
      await removeFromFavorites(sceneId);
    } else {
      await addToFavorites(sceneId);
    }
  }

  /// 检查场景是否已收藏
  bool isFavorite(String sceneId) {
    return favoriteScenes.contains(sceneId);
  }

  /// 设置当前选中的分组
  void setSelectedGroup(String groupId) {
    selectedGroupId.value = groupId;
  }

  /// 切换收藏场景显示状态
  void toggleShowFavorites() {
    showFavorites.value = !showFavorites.value;
  }

  /// 设置搜索关键字
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// 清除搜索
  void clearSearch() {
    searchQuery.value = '';
  }

  /// 获取过滤后的场景列表
  List<SceneModel> get filteredScenes {
    // 基础列表
    List<SceneModel> result = List.from(scenes);

    // 应用分组过滤
    if (selectedGroupId.value != 'all') {
      result = result.where((scene) => scene.groupId == selectedGroupId.value).toList();
    }

    // 应用收藏过滤
    if (showFavorites.value) {
      result = result.where((scene) => favoriteScenes.contains(scene.id)).toList();
    }

    // 应用搜索过滤
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      result = result.where((scene) =>
        scene.name.toLowerCase().contains(query) ||
        scene.id.toLowerCase().contains(query)
      ).toList();
    }

    return result;
  }

  /// 获取指定分组的场景列表
  List<SceneModel> getScenesInGroup(String groupId) {
    return scenes.where((scene) => scene.groupId == groupId).toList();
  }

  /// 获取未分组的场景列表
  List<SceneModel> getUngroupedScenes() {
    return scenes.where((scene) => scene.groupId == null).toList();
  }

  /// 触发场景
  Future<void> activateScene(SceneModel scene) async {
    try {
      final success = await _sceneRepository.activateScene(scene);

      if (success) {
        Get.snackbar(
          '成功',
          '已触发场景: ${scene.name}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          '失败',
          '触发场景失败: ${scene.name}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LogService.to.e('触发场景失败', e);
      Get.snackbar(
        '错误',
        '触发场景时发生错误: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 删除场景
  Future<void> deleteScene(SceneModel scene) async {
    try {
      // 只能删除自定义场景
      if (!scene.isCustom) {
        Get.snackbar(
          '无法删除',
          '内置场景无法删除',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      final success = await _sceneRepository.deleteScene(scene.id);

      if (success) {
        // 从收藏中移除
        if (favoriteScenes.contains(scene.id)) {
          await removeFromFavorites(scene.id);
        }

        // 重新加载场景列表
        await loadScenes();

        Get.snackbar(
          '成功',
          '已删除场景: ${scene.name}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '失败',
          '删除场景失败: ${scene.name}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LogService.to.e('删除场景失败', e);
      Get.snackbar(
        '错误',
        '删除场景时发生错误: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
