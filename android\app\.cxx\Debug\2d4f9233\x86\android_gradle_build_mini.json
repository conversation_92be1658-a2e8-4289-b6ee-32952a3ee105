{"buildFiles": ["D:\\software\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\2d4f9233\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\2d4f9233\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}