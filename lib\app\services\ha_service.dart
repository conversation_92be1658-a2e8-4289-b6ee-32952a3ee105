import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import '../data/models/entity_model.dart';
import '../data/models/ha_response_model.dart';
import '../data/local/storage_keys.dart';
import '../data/local/storage_service.dart';
import 'websocket_service.dart';
import 'log_service.dart';

/// Home Assistant服务，提供与Home Assistant交互的高级API
class HaService extends GetxService {
  // WebSocket服务
  final WebSocketService _wsService = Get.find<WebSocketService>();

  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();

  // 实体列表
  final RxList<EntityModel> entities = <EntityModel>[].obs;

  // 实体映射（entityId -> EntityModel）
  final _entityMap = <String, EntityModel>{}.obs;

  // 是否使用缓存数据
  final RxBool isUsingCachedData = false.obs;

  // 消息响应映射（id -> Completer）
  final _responseCompleters = <int, Completer<HaResponseModel>>{};

  // 消息订阅
  StreamSubscription? _subscription;

  // 获取下一个请求ID (使用WebSocketService的ID生成器)
  int _getNextRequestId() {
    // 使用WebSocketService的ID生成器，确保所有消息ID连续递增
    return _wsService.getNextMessageId();
  }

  // 最后一次获取实体的时间
  DateTime _lastFetchTime = DateTime.now().subtract(const Duration(minutes: 5));

  // 是否已经初始化加载过实体
  bool _hasInitiallyLoadedEntities = false;

  // 是否自动加载实体（默认为true，在登录成功后自动加载实体列表）
  final RxBool autoLoadEntities = true.obs;

  /// 初始化Home Assistant服务
  Future<HaService> init() async {
    // 订阅WebSocket消息
    _subscription = _wsService.onMessage.listen(_handleMessages);

    // 监听WebSocket连接状态
    _wsService.isConnected.listen((connected) {
      if (connected) {
        // 连接成功后，如果设置了自动加载且尚未加载过，则获取所有实体状态
        if (autoLoadEntities.value && entities.isEmpty && !_hasInitiallyLoadedEntities) {
          LogService.to.i('WebSocket连接成功，自动加载实体数据...');
          _hasInitiallyLoadedEntities = true;
          loadInitialEntities();
        }

        // 连接恢复后，如果正在使用缓存数据，重置标志
        if (isUsingCachedData.value) {
          LogService.to.i('WebSocket连接已恢复，不再使用缓存数据');
          isUsingCachedData.value = false;
        }
      } else {
        // 连接断开前，保存当前实体数据到缓存
        if (entities.isNotEmpty) {
          LogService.to.i('WebSocket连接断开，保存实体数据到缓存...');
          saveEntitiesToCache();
        }

        // 尝试从缓存加载数据
        loadEntitiesFromCache().then((success) {
          if (!success) {
            LogService.to.w('无法从缓存加载数据，清空实体列表');
            // 如果无法从缓存加载，则清空实体列表
            entities.clear();
            _entityMap.clear();
          }
        });

        // 重置初始加载标志
        _hasInitiallyLoadedEntities = false;
      }
    });

    return this;
  }

  /// 加载初始实体数据
  Future<void> loadInitialEntities() async {
    if (!_wsService.isConnected.value) {
      LogService.to.w('WebSocket未连接，无法加载初始实体数据');
      return;
    }

    LogService.to.i('开始加载初始实体数据...');

    try {
      // 获取初始数据，使用自动生成的ID
      final response = await _sendCommand({
        'type': 'get_states'
      });

      if (response.result != null) {
        final List<dynamic> result = response.result as List<dynamic>;

        LogService.to.i('解析初始实体数据，数量: ${result.length}');

        final List<EntityModel> newEntities = result
            .map((item) {
              try {
                return EntityModel.fromJson(item as Map<String, dynamic>);
              } catch (e) {
                LogService.to.e('解析实体失败', e);
                return null;
              }
            })
            .where((entity) => entity != null)
            .cast<EntityModel>()
            .toList();

        // 清空现有实体
        entities.clear();
        _entityMap.clear();

        // 添加新实体
        for (final entity in newEntities) {
          _updateEntity(entity);
        }

        LogService.to.i('初始加载完成，获取到 ${entities.length} 个实体');
        if (entities.isNotEmpty) {
          LogService.to.d('第一个实体示例: ${entities.first.entityId} - ${entities.first.friendlyName}');
        }
      }
    } catch (e) {
      LogService.to.e('初始加载实体失败', e);
    }
  }

  /// 处理WebSocket消息
  void _handleMessages(Map<String, dynamic> message) {
    try {
      final response = HaResponseModel.fromJson(message);

      // 记录接收到的消息类型
      LogService.to.d('接收到WebSocket消息: type=${response.type}, id=${response.id}');

      // 处理事件消息
      if (response.type == 'event' && message.containsKey('event')) {
        final eventData = message['event'];
        final eventType = eventData['event_type'];

        if (eventType == 'state_changed' && eventData.containsKey('data')) {
          final stateData = eventData['data'];
          final newState = stateData['new_state'];
          final entityId = stateData['entity_id'];

          if (newState != null) {
            try {
              // 只更新实体映射，不触发实体列表的变化
              final entity = EntityModel.fromJson(newState);

              // 只更新实体映射，不更新实体列表
              _entityMap[entity.entityId] = entity;

              // 记录状态变化但不触发UI更新
              LogService.to.d('实体状态变化: ${entity.entityId} -> ${entity.state}');
            } catch (e) {
              LogService.to.e('解析实体状态失败: $entityId', e);
            }
          }
        }
      }

      // 处理响应消息
      if (response.id != null) {
        // 检查是否有等待此ID的响应
        if (_responseCompleters.containsKey(response.id)) {
          LogService.to.d('处理响应消息: id=${response.id}, success=${response.success}');
          final completer = _responseCompleters.remove(response.id);
          completer?.complete(response);
        } else {
          // 如果没有等待此ID的响应，记录警告
          LogService.to.w('收到ID=${response.id}的响应，但没有对应的等待请求');

          // 检查是否有其他等待中的请求
          if (_responseCompleters.isNotEmpty) {
            LogService.to.d('当前等待响应的请求ID: ${_responseCompleters.keys.join(', ')}');
          }
        }
      }
    } catch (e, stackTrace) {
      LogService.to.e('处理WebSocket消息失败', e, stackTrace);

      // 尝试记录原始消息
      try {
        LogService.to.d('原始消息: ${jsonEncode(message)}');
      } catch (_) {
        LogService.to.d('无法编码原始消息');
      }
    }
  }

  /// 更新实体
  ///
  /// 此方法会同时更新实体映射和实体列表，并触发实体列表的变化通知
  /// 只在批量更新实体时使用，如初始加载或强制刷新
  void _updateEntity(EntityModel entity) {
    // 更新实体映射
    _entityMap[entity.entityId] = entity;

    // 更新实体列表
    final index = entities.indexWhere((e) => e.entityId == entity.entityId);
    if (index >= 0) {
      entities[index] = entity;
    } else {
      entities.add(entity);
    }

    // 按域名和友好名称排序
    entities.sort((a, b) {
      final domainCompare = a.domain.compareTo(b.domain);
      if (domainCompare != 0) return domainCompare;
      return a.friendlyName.compareTo(b.friendlyName);
    });
  }

  /// 同步实体映射到实体列表
  ///
  /// 在需要将实体映射中的变化同步到实体列表时调用
  /// 例如在用户点击刷新按钮时
  void syncEntitiesToList() {
    LogService.to.i('同步实体映射到实体列表...');

    // 清空现有实体列表
    entities.clear();

    // 将实体映射中的所有实体添加到列表
    entities.addAll(_entityMap.values);

    // 按域名和友好名称排序
    entities.sort((a, b) {
      final domainCompare = a.domain.compareTo(b.domain);
      if (domainCompare != 0) return domainCompare;
      return a.friendlyName.compareTo(b.friendlyName);
    });

    LogService.to.i('同步完成，实体列表数量: ${entities.length}');
  }

  /// 获取所有实体状态
  /// [forceRefresh] 是否强制刷新数据
  Future<List<EntityModel>> fetchAllEntities({bool forceRefresh = false}) async {
    try {
      // 检查WebSocket是否连接
      if (!_wsService.isConnected.value) {
        LogService.to.w('WebSocket未连接，无法获取实时数据');

        // 如果已经有实体数据（可能是缓存的），直接返回
        if (entities.isNotEmpty) {
          LogService.to.i('WebSocket未连接，使用现有数据，数量: ${entities.length}');
          return entities;
        }

        // 尝试从缓存加载
        final success = await loadEntitiesFromCache();
        if (success) {
          LogService.to.i('WebSocket未连接，已从缓存加载数据，数量: ${entities.length}');
          return entities;
        }

        // 如果没有缓存数据，抛出异常
        throw Exception('WebSocket未连接且没有缓存数据');
      }

      // 检查是否需要刷新
      final now = DateTime.now();
      final timeSinceLastFetch = now.difference(_lastFetchTime);

      // 如果已经有实体数据且不是强制刷新，且距离上次获取时间不超过10秒，直接返回
      if (entities.isNotEmpty && !forceRefresh && timeSinceLastFetch.inSeconds < 10) {
        LogService.to.d('使用现有实体数据，数量: ${entities.length}，距离上次获取: ${timeSinceLastFetch.inSeconds}秒');
        return entities;
      }

      // 更新最后获取时间
      _lastFetchTime = now;

      // 如果是强制刷新，先记录当前实体数量
      if (forceRefresh) {
        LogService.to.i('强制刷新，当前实体数量: ${entities.length}');
      }

      LogService.to.i('获取所有实体状态...');

      // 记录当前等待响应的请求
      if (_responseCompleters.isNotEmpty) {
        LogService.to.d('发送新请求前，当前有 ${_responseCompleters.length} 个等待响应的请求，ID列表: ${_responseCompleters.keys.join(', ')}');
      }

      // 发送请求，ID会在_sendCommand中自动生成
      final response = await _sendCommand({
        'type': 'get_states'
      });

      // 记录响应信息
      LogService.to.i('收到获取实体状态响应: id=${response.id}');

      if (response.result != null) {
        final List<dynamic> result = response.result as List<dynamic>;

        LogService.to.i('解析实体数据，数量: ${result.length}');

        final List<EntityModel> newEntities = result
            .map((item) {
              try {
                return EntityModel.fromJson(item as Map<String, dynamic>);
              } catch (e) {
                LogService.to.e('解析实体失败', e);
                return null;
              }
            })
            .where((entity) => entity != null)
            .cast<EntityModel>()
            .toList();

        // 保存当前实体数量
        final oldEntitiesCount = entities.length;

        // 只有在确实有新数据时才清空现有实体
        if (newEntities.isNotEmpty) {
          // 清空现有实体
          entities.clear();
          _entityMap.clear();

          // 添加新实体
          for (final entity in newEntities) {
            _updateEntity(entity);
          }

          LogService.to.i('更新了实体列表：从 $oldEntitiesCount 个更新为 ${entities.length} 个');

          // 保存到缓存
          saveEntitiesToCache();

          // 重置缓存标志
          isUsingCachedData.value = false;
        } else {
          LogService.to.w('获取到的新实体列表为空，保留现有 $oldEntitiesCount 个实体');
        }

        LogService.to.i('获取到 ${entities.length} 个实体');
        if (entities.isNotEmpty) {
          LogService.to.d('第一个实体示例: ${entities.first.entityId} - ${entities.first.friendlyName}');
        }

        return entities;
      } else {
        LogService.to.w('响应中没有result字段: ${jsonEncode(response.toJson())}');

        // 如果响应中没有result字段，但已有实体数据，则返回现有数据
        if (entities.isNotEmpty) {
          LogService.to.i('响应无效，但保留现有实体数据，数量: ${entities.length}');
          return entities;
        }
      }

      // 只有在没有任何实体数据时才返回空列表
      LogService.to.w('没有获取到任何实体数据，返回空列表');
      return [];
    } catch (e, stackTrace) {
      LogService.to.e('获取实体状态失败', e, stackTrace);

      // 如果发生异常但已有实体数据，则返回现有数据
      if (entities.isNotEmpty) {
        LogService.to.i('发生异常，但保留现有实体数据，数量: ${entities.length}');
        return entities;
      }

      // 只有在没有任何实体数据时才返回空列表
      return [];
    }
  }

  /// 获取实体状态
  EntityModel? getEntity(String entityId) {
    return _entityMap[entityId];
  }

  /// 获取指定域名的实体
  List<EntityModel> getEntitiesByDomain(String domain) {
    return entities.where((entity) => entity.domain == domain).toList();
  }

  /// 获取可控制的实体（支持开关操作的实体）
  List<EntityModel> getControllableEntities() {
    return entities.where((entity) => entity.supportsTurnOn).toList();
  }

  /// 获取传感器实体
  List<EntityModel> getSensorEntities() {
    return entities.where((entity) => entity.isSensor).toList();
  }

  /// 调用服务
  Future<HaResponseModel> callService(String domain, String service, {
    String? entityId,
    Map<String, dynamic>? serviceData,
  }) async {
    final data = serviceData ?? {};

    if (entityId != null) {
      data['entity_id'] = entityId;
    }

    return _sendCommand({
      'type': 'call_service',
      'domain': domain,
      'service': service,
      'service_data': data,
    });
  }

  /// 打开实体
  Future<HaResponseModel> turnOn(String entityId, {Map<String, dynamic>? data}) async {
    final domain = entityId.split('.').first;
    return callService(domain, 'turn_on', entityId: entityId, serviceData: data);
  }

  /// 关闭实体
  Future<HaResponseModel> turnOff(String entityId, {Map<String, dynamic>? data}) async {
    final domain = entityId.split('.').first;
    return callService(domain, 'turn_off', entityId: entityId, serviceData: data);
  }

  /// 切换实体状态
  Future<HaResponseModel> toggle(String entityId) async {
    final domain = entityId.split('.').first;
    return callService(domain, 'toggle', entityId: entityId);
  }

  /// 发送命令并等待响应
  Future<HaResponseModel> _sendCommand(Map<String, dynamic> command) async {
    if (!_wsService.isConnected.value) {
      LogService.to.e('WebSocket未连接，无法发送命令');

      // 创建一个错误响应而不是抛出异常
      return HaResponseModel(
        type: 'result',
        id: command['id'] as int? ?? 0,
        success: false,
        error: 'WebSocket未连接，请检查网络连接',
      );
    }

    // 获取一个新的ID，确保所有消息ID连续递增
    final id = _getNextRequestId();

    // 设置命令ID
    command['id'] = id;

    final completer = Completer<HaResponseModel>();

    // 记录请求信息
    LogService.to.d('准备发送命令: type=${command['type']}, id=$id');

    // 存储completer以便在收到响应时完成
    _responseCompleters[id] = completer;

    try {
      // 发送消息前检查是否已有相同ID的响应等待中
      if (_responseCompleters.length > 1) {
        LogService.to.d('当前有 ${_responseCompleters.length} 个等待响应的请求，ID列表: ${_responseCompleters.keys.join(', ')}');
      }

      // 发送消息
      await _wsService.sendMessage(command);

      // 设置超时（增加到60秒，以处理大量实体）
      return completer.future.timeout(
        const Duration(seconds: 60),
        onTimeout: () {
          // 超时时移除completer
          _responseCompleters.remove(id);
          final commandType = command['type'] as String?;
          LogService.to.w('命令超时: type=$commandType, id=$id');

          // 如果是获取状态命令，返回一个空结果而不是抛出异常
          if (commandType == 'get_states') {
            LogService.to.i('获取状态命令超时，返回空结果');
            return HaResponseModel(
              type: 'result',
              id: id,
              success: true,
              result: [],
            );
          }

          throw TimeoutException('命令超时: type=$commandType, id=$id');
        },
      );
    } catch (e) {
      LogService.to.e('发送命令失败', e);
      completer.completeError(e);
      return completer.future;
    }
  }

  /// 保存实体数据到缓存
  Future<void> saveEntitiesToCache() async {
    if (entities.isEmpty) {
      LogService.to.w('实体列表为空，不保存缓存');
      return;
    }

    try {
      // 将实体列表转换为JSON
      final entitiesJson = entities.map((entity) => entity.toJson()).toList();

      // 保存到存储
      await _storageService.saveJson(StorageKeys.cachedEntities, entitiesJson);

      // 保存缓存时间
      await _storageService.saveString(
        StorageKeys.lastCacheTime,
        DateTime.now().toIso8601String()
      );

      LogService.to.i('已保存 ${entities.length} 个实体到缓存');
    } catch (e) {
      LogService.to.e('保存实体缓存失败', e);
    }
  }

  /// 从缓存加载实体数据
  Future<bool> loadEntitiesFromCache() async {
    try {
      // 获取缓存的实体数据
      final cachedData = _storageService.getJson(StorageKeys.cachedEntities);

      if (cachedData == null) {
        LogService.to.w('没有找到缓存的实体数据');
        return false;
      }

      // 获取缓存时间
      final lastCacheTimeStr = _storageService.getString(StorageKeys.lastCacheTime);
      DateTime? lastCacheTime;

      if (lastCacheTimeStr != null) {
        try {
          lastCacheTime = DateTime.parse(lastCacheTimeStr);
          final now = DateTime.now();
          final difference = now.difference(lastCacheTime);

          // 记录缓存时间
          LogService.to.i('缓存时间: ${lastCacheTime.toString()}, 距今: ${difference.inHours}小时${difference.inMinutes % 60}分钟');
        } catch (e) {
          LogService.to.w('解析缓存时间失败: $e');
        }
      }

      // 解析实体数据
      final List<dynamic> entitiesJson = cachedData as List<dynamic>;

      final List<EntityModel> cachedEntities = entitiesJson
          .map((item) {
            try {
              return EntityModel.fromJson(item as Map<String, dynamic>);
            } catch (e) {
              LogService.to.e('解析缓存实体失败', e);
              return null;
            }
          })
          .where((entity) => entity != null)
          .cast<EntityModel>()
          .toList();

      if (cachedEntities.isEmpty) {
        LogService.to.w('缓存的实体列表为空');
        return false;
      }

      // 清空现有实体
      entities.clear();
      _entityMap.clear();

      // 添加缓存的实体
      for (final entity in cachedEntities) {
        _updateEntity(entity);
      }

      // 标记为使用缓存数据
      isUsingCachedData.value = true;

      LogService.to.i('从缓存加载了 ${entities.length} 个实体');
      return true;
    } catch (e) {
      LogService.to.e('加载缓存实体失败', e);
      return false;
    }
  }

  @override
  void onClose() {
    _subscription?.cancel();
    super.onClose();
  }
}
