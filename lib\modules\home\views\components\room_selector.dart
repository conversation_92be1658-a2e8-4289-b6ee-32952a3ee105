import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/config/app_routes.dart';
import '../../controllers/home_controller.dart';
import 'dart:math' as math;

/// 房间选择器组件
class RoomSelector extends GetView<HomeController> {
  const RoomSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 房间选择器
        Container(
          height: 90, 
          margin: const EdgeInsets.only(top: 8, bottom: 4),
          child: Obx(() {
            // 创建一个包含所有房间的列表（包括"全部房间"）
            final allRooms = [null, ...controller.rooms];

            // 获取当前选中的房间索引
            final selectedIndex = controller.selectedRoom.value == null
                ? 0
                : allRooms.indexWhere((r) => r?.id == controller.selectedRoom.value?.id);

            // 使用ListView实现水平滚动，自动调整宽度
            return Container(
              padding: const EdgeInsets.only(left: 16), // 添加左侧padding
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                child: Row(
                  children: List.generate(allRooms.length, (index) {
                    final room = allRooms[index];
                    final isSelected = index == selectedIndex;

                    // 全部房间选项
                    if (room == null) {
                      return _buildRoomCard(
                        context,
                        null,
                        'all_rooms',
                        '全部房间',
                        isSelected,
                        Icons.home,
                      );
                    }

                    // 房间选项
                    return _buildRoomCard(
                      context,
                      room,
                      room.id,
                      room.name,
                      isSelected,
                      _getRoomIcon(room.type, room.icon),
                    );
                  }),
                ),
              ),
            );
          }),
        ),

        // 房间管理按钮
        Align(
          alignment: Alignment.centerRight,
          child: TextButton.icon(
            onPressed: () => Get.toNamed(AppRoutes.roomManager),
            icon: const Icon(Icons.settings, size: 16),
            label: const Text('房间管理'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              minimumSize: const Size(0, 24),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
          ),
        ),
      ],
    );
  }

  /// 获取房间图标
  IconData _getRoomIcon(String type, String? iconName) {
    if (iconName != null) {
      // 尝试从iconName获取图标
      switch (iconName) {
        case 'weekend': return Icons.weekend;
        case 'bed': return Icons.bed;
        case 'kitchen': return Icons.kitchen;
        case 'bathtub': return Icons.bathtub;
        case 'computer': return Icons.computer;
        case 'dining': return Icons.dining;
        case 'local_laundry_service': return Icons.local_laundry_service;
        case 'garage': return Icons.garage;
        case 'balcony': return Icons.balcony;
        case 'home': return Icons.home;
      }
    }

    // 根据房间类型返回默认图标
    switch (type) {
      case 'living_room': return Icons.weekend;
      case 'bedroom': return Icons.bed;
      case 'kitchen': return Icons.kitchen;
      case 'bathroom': return Icons.bathtub;
      case 'office': return Icons.computer;
      case 'dining_room': return Icons.dining;
      case 'laundry_room': return Icons.local_laundry_service;
      case 'garage': return Icons.garage;
      case 'balcony': return Icons.balcony;
      default: return Icons.home;
    }
  }

  /// 构建房间卡片
  Widget _buildRoomCard(
    BuildContext context,
    RoomModel? room,
    String id,
    String name,
    bool isSelected,
    IconData icon,
  ) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final backgroundColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF303030)
        : Colors.white;
    final selectedColor = primaryColor;
    final unselectedColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white.withOpacity(0.7)
        : Colors.black.withOpacity(0.7);

    return GestureDetector(
      onTap: () {
        controller.selectRoom(room);
      },
      child: Container(
        width: 70, // 固定宽度
        margin: const EdgeInsets.only(right: 12, top: 4, bottom: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标容器
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: isSelected ? selectedColor : backgroundColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: isSelected
                        ? selectedColor.withOpacity(0.3)
                        : Colors.black.withOpacity(0.1),
                    blurRadius: isSelected ? 8 : 4,
                    spreadRadius: isSelected ? 1 : 0,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: isSelected
                      ? selectedColor
                      : Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withOpacity(0.1)
                          : Colors.black.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withOpacity(0.9)
                        : Colors.black.withOpacity(0.7),
                size: 28,
              ),
            ),

            // 文字标签
            const SizedBox(height: 6),
            Text(
              name,
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }


}
