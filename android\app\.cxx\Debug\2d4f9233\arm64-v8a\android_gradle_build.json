{"buildFiles": ["D:\\software\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\2d4f9233\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\software\\android-sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\appProject\\ha_smart_home\\android\\app\\.cxx\\Debug\\2d4f9233\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\software\\android-sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\software\\android-sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}