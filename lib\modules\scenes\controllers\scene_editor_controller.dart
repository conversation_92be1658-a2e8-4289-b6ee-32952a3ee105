import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/repositories/scene_repository.dart';
import '../../../app/data/models/scene_model.dart';
import '../../../app/data/models/scene_group_model.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../app/services/ha_service.dart';
import '../../../app/services/log_service.dart';

/// 场景编辑控制器
class SceneEditorController extends GetxController {
  // 场景仓库
  final SceneRepository _sceneRepository = Get.find<SceneRepository>();

  // Home Assistant服务
  final HaService _haService = Get.find<HaService>();

  // 当前编辑的场景
  final Rx<SceneModel?> currentScene = Rx<SceneModel?>(null);

  // 场景名称
  final RxString sceneName = ''.obs;

  // 场景图标
  final Rx<String?> sceneIcon = Rx<String?>(null);

  // 场景颜色
  final Rx<MaterialColor> sceneColor = Colors.blue.obs;

  // 场景分组ID
  final Rx<String?> sceneGroupId = Rx<String?>(null);

  // 场景实体ID列表
  final RxList<String> sceneEntityIds = <String>[].obs;

  // 场景分组列表
  final RxList<SceneGroupModel> sceneGroups = <SceneGroupModel>[].obs;

  // 可选实体列表
  final RxList<EntityModel> availableEntities = <EntityModel>[].obs;

  // 已选实体列表
  final RxList<EntityModel> selectedEntities = <EntityModel>[].obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 保存状态
  final RxBool isSaving = false.obs;

  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);

  // 搜索关键字
  final RxString searchQuery = ''.obs;

  // 是否是编辑模式
  final RxBool isEditMode = false.obs;

  @override
  void onInit() {
    super.onInit();

    // 加载场景分组
    loadSceneGroups();

    // 加载可用实体
    loadAvailableEntities();

    // 检查是否有传入的场景数据
    if (Get.arguments != null) {
      // 编辑现有场景
      initEditor(scene: Get.arguments);
    } else {
      // 创建新场景
      initEditor();
    }
  }

  /// 初始化编辑器
  void initEditor({SceneModel? scene}) {
    if (scene != null) {
      // 编辑现有场景
      currentScene.value = scene;
      sceneName.value = scene.name;
      sceneIcon.value = scene.icon;
      // 将Color转换为MaterialColor
      sceneColor.value = _convertToMaterialColor(scene.color);
      sceneGroupId.value = scene.groupId;
      sceneEntityIds.value = List.from(scene.entityIds);
      isEditMode.value = true;

      // 加载已选实体
      loadSelectedEntities();
    } else {
      // 创建新场景
      currentScene.value = null;
      sceneName.value = '新场景';
      sceneIcon.value = 'auto_awesome';
      sceneColor.value = Colors.blue;
      sceneGroupId.value = null;
      sceneEntityIds.clear();
      selectedEntities.clear();
      isEditMode.value = false;
    }
  }

  /// 加载场景分组
  Future<void> loadSceneGroups() async {
    try {
      // 获取所有场景分组
      final groups = await _sceneRepository.getAllSceneGroups();

      // 更新分组列表
      sceneGroups.value = groups;
    } catch (e) {
      LogService.to.e('加载场景分组失败', e);
    }
  }

  /// 加载可用实体
  void loadAvailableEntities() {
    try {
      // 获取所有可控制的实体
      final entities = _haService.getControllableEntities();

      // 更新可用实体列表
      availableEntities.value = entities;
    } catch (e) {
      LogService.to.e('加载可用实体失败', e);
    }
  }

  /// 加载已选实体
  void loadSelectedEntities() {
    try {
      // 清空已选实体列表
      selectedEntities.clear();

      // 确保可用实体已加载
      if (availableEntities.isEmpty) {
        loadAvailableEntities();
      }

      LogService.to.i('加载已选实体，场景实体ID数量: ${sceneEntityIds.length}');

      // 遍历场景实体ID
      for (final entityId in sceneEntityIds) {
        // 查找对应的实体
        final entity = _haService.getEntity(entityId);

        // 如果找到实体，添加到已选列表
        if (entity != null) {
          selectedEntities.add(entity);
          LogService.to.i('已添加实体: ${entity.entityId} - ${entity.friendlyName}');
        } else {
          LogService.to.w('找不到实体: $entityId');
        }
      }

      LogService.to.i('已选实体加载完成，数量: ${selectedEntities.length}');
    } catch (e) {
      LogService.to.e('加载已选实体失败', e);
      errorMessage.value = '加载已选设备失败: $e';
    }
  }

  /// 设置场景名称
  void setSceneName(String name) {
    sceneName.value = name;
  }

  /// 设置场景图标
  void setSceneIcon(String icon) {
    sceneIcon.value = icon;
  }

  /// 设置场景颜色
  void setSceneColor(Color color) {
    sceneColor.value = _convertToMaterialColor(color);
  }

  /// 将Color转换为MaterialColor
  MaterialColor _convertToMaterialColor(Color color) {
    // 如果已经是MaterialColor，直接返回
    if (color is MaterialColor) {
      return color;
    }

    // 尝试匹配最接近的MaterialColor
    final materialColors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];

    // 找到颜色值最接近的MaterialColor
    MaterialColor closestColor = Colors.blue;
    int minDifference = 0xFFFFFFFF;

    for (final materialColor in materialColors) {
      final difference = _colorDifference(color, materialColor);
      if (difference < minDifference) {
        minDifference = difference;
        closestColor = materialColor;
      }
    }

    return closestColor;
  }

  /// 计算两个颜色之间的差异值
  int _colorDifference(Color a, Color b) {
    // 使用推荐的组件访问器
    final rDiff = (a.r - b.r).abs();
    final gDiff = (a.g - b.g).abs();
    final bDiff = (a.b - b.b).abs();
    return (rDiff + gDiff + bDiff).toInt();
  }

  /// 设置场景分组
  void setSceneGroup(String? groupId) {
    sceneGroupId.value = groupId;
  }

  /// 添加实体到场景
  void addEntity(EntityModel entity) {
    if (!sceneEntityIds.contains(entity.entityId)) {
      sceneEntityIds.add(entity.entityId);
      selectedEntities.add(entity);
    }
  }

  /// 从场景中移除实体
  void removeEntity(EntityModel entity) {
    sceneEntityIds.remove(entity.entityId);
    selectedEntities.removeWhere((e) => e.entityId == entity.entityId);
  }

  /// 设置搜索关键字
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// 清除搜索
  void clearSearch() {
    searchQuery.value = '';
  }

  /// 获取过滤后的可用实体列表
  List<EntityModel> get filteredAvailableEntities {
    if (searchQuery.value.isEmpty) {
      return availableEntities;
    }

    final query = searchQuery.value.toLowerCase();
    return availableEntities.where((entity) =>
      entity.friendlyName.toLowerCase().contains(query) ||
      entity.entityId.toLowerCase().contains(query)
    ).toList();
  }

  /// 保存场景
  Future<bool> saveScene() async {
    try {
      isSaving.value = true;
      errorMessage.value = null;

      // 验证场景名称
      if (sceneName.value.isEmpty) {
        errorMessage.value = '场景名称不能为空';
        isSaving.value = false;
        return false;
      }

      // 验证场景实体
      if (sceneEntityIds.isEmpty) {
        errorMessage.value = '请至少选择一个设备';
        isSaving.value = false;
        return false;
      }

      // 创建场景模型
      final scene = SceneModel(
        id: currentScene.value?.id ?? 'scene_${DateTime.now().millisecondsSinceEpoch}',
        name: sceneName.value,
        icon: sceneIcon.value,
        color: sceneColor.value,
        groupId: sceneGroupId.value,
        entityIds: sceneEntityIds,
        order: currentScene.value?.order ?? 0,
        isCustom: true,
      );

      // 保存场景
      final success = await _sceneRepository.saveScene(scene);

      isSaving.value = false;

      if (success) {
        Get.snackbar(
          '成功',
          '场景已保存',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        errorMessage.value = '保存场景失败';
        return false;
      }
    } catch (e) {
      isSaving.value = false;
      errorMessage.value = '保存场景时发生错误: $e';
      LogService.to.e('保存场景失败', e);
      return false;
    }
  }

  /// 创建场景分组
  Future<bool> createSceneGroup(String name, String? icon) async {
    try {
      // 创建分组模型
      final group = SceneGroupModel(
        id: 'group_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        icon: icon,
        order: sceneGroups.length,
      );

      // 保存分组
      final success = await _sceneRepository.saveSceneGroup(group);

      if (success) {
        // 重新加载分组列表
        await loadSceneGroups();

        // 设置当前场景的分组
        setSceneGroup(group.id);

        return true;
      } else {
        return false;
      }
    } catch (e) {
      LogService.to.e('创建场景分组失败', e);
      return false;
    }
  }
}
