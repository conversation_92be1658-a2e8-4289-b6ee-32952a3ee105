{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/software/android-sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/software/android-sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/software/android-sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/software/android-sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-b7e944f028ccb1d32d1d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e88b8c42bad6bce4005d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-926531656c4393376c78.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e88b8c42bad6bce4005d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-926531656c4393376c78.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-b7e944f028ccb1d32d1d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}