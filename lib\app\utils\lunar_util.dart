import 'package:intl/intl.dart';

/// 农历工具类
class LunarUtil {
  /// 天干
  static const List<String> tianGan = [
    '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'
  ];
  
  /// 地支
  static const List<String> diZhi = [
    '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'
  ];
  
  /// 生肖
  static const List<String> animals = [
    '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'
  ];
  
  /// 农历月份
  static const List<String> lunarMonths = [
    '正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'
  ];
  
  /// 农历日期
  static const List<String> lunarDays = [
    '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
    '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
    '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
  ];
  
  /// 节气
  static const List<String> solarTerms = [
    '小寒', '大寒', '立春', '雨水', '惊蛰', '春分',
    '清明', '谷雨', '立夏', '小满', '芒种', '夏至',
    '小暑', '大暑', '立秋', '处暑', '白露', '秋分',
    '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
  ];
  
  /// 节气日期（公历）
  static const Map<String, List<int>> solarTermDates = {
    '2023': [5, 20, 4, 19, 6, 21, 5, 20, 6, 21, 6, 21, 7, 23, 8, 23, 8, 23, 8, 24, 7, 22, 7, 22],
    '2024': [5, 20, 4, 19, 5, 20, 4, 20, 5, 21, 5, 21, 7, 22, 7, 23, 7, 23, 8, 23, 7, 22, 7, 21],
    '2025': [5, 20, 3, 18, 5, 20, 4, 20, 5, 21, 5, 21, 7, 22, 7, 23, 7, 23, 8, 23, 7, 22, 6, 21],
  };
  
  /// 节日
  static const Map<String, String> festivals = {
    '01-01': '元旦',
    '02-14': '情人节',
    '03-08': '妇女节',
    '04-01': '愚人节',
    '05-01': '劳动节',
    '06-01': '儿童节',
    '10-01': '国庆节',
    '12-25': '圣诞节',
  };
  
  /// 农历节日
  static const Map<String, String> lunarFestivals = {
    '01-01': '春节',
    '01-15': '元宵节',
    '05-05': '端午节',
    '07-07': '七夕节',
    '08-15': '中秋节',
    '09-09': '重阳节',
    '12-30': '除夕',
  };
  
  /// 获取农历年份
  static String getLunarYear(DateTime date) {
    // 简化实现，实际应该根据农历历法计算
    // 这里使用公历年份代替
    final year = date.year;
    final offset = (year - 4) % 60;
    final tianGanIndex = offset % 10;
    final diZhiIndex = offset % 12;
    
    return '${tianGan[tianGanIndex]}${diZhi[diZhiIndex]}年';
  }
  
  /// 获取生肖
  static String getAnimal(DateTime date) {
    // 简化实现，实际应该根据农历历法计算
    // 这里使用公历年份代替
    final year = date.year;
    final offset = (year - 4) % 12;
    
    return animals[offset];
  }
  
  /// 获取农历月份
  static String getLunarMonth(DateTime date) {
    // 简化实现，实际应该根据农历历法计算
    // 这里使用公历月份代替
    final month = date.month - 1;
    
    return '${lunarMonths[month]}月';
  }
  
  /// 获取农历日期
  static String getLunarDay(DateTime date) {
    // 简化实现，实际应该根据农历历法计算
    // 这里使用公历日期代替
    final day = (date.day - 1) % 30;
    
    return lunarDays[day];
  }
  
  /// 获取节气
  static String? getSolarTerm(DateTime date) {
    final year = date.year.toString();
    final month = date.month;
    final day = date.day;
    
    // 检查是否有当年的节气数据
    if (solarTermDates.containsKey(year)) {
      final dates = solarTermDates[year]!;
      
      // 遍历节气日期
      for (int i = 0; i < dates.length; i++) {
        // 节气月份（从1开始）
        final termMonth = (i ~/ 2) + 1;
        // 节气日期
        final termDay = dates[i];
        
        // 如果日期匹配，返回对应节气
        if (month == termMonth && day == termDay) {
          return solarTerms[i];
        }
      }
    }
    
    return null;
  }
  
  /// 获取节日
  static String? getFestival(DateTime date) {
    final formattedDate = DateFormat('MM-dd').format(date);
    
    return festivals[formattedDate];
  }
  
  /// 获取农历节日
  static String? getLunarFestival(DateTime date) {
    // 简化实现，实际应该根据农历历法计算
    // 这里使用公历日期代替
    final month = date.month;
    final day = date.day;
    final formattedDate = '$month-$day';
    
    return lunarFestivals[formattedDate];
  }
  
  /// 获取完整农历日期
  static String getFullLunarDate(DateTime date) {
    final lunarYear = getLunarYear(date);
    final lunarMonth = getLunarMonth(date);
    final lunarDay = getLunarDay(date);
    
    return '$lunarYear $lunarMonth$lunarDay';
  }
  
  /// 获取日期信息
  static Map<String, String> getDateInfo(DateTime date) {
    final weekday = _getWeekdayName(date.weekday);
    final lunarDate = getFullLunarDate(date);
    final animal = getAnimal(date);
    final solarTerm = getSolarTerm(date);
    final festival = getFestival(date);
    final lunarFestival = getLunarFestival(date);
    
    final result = <String, String>{
      'weekday': weekday,
      'lunarDate': lunarDate,
      'animal': animal,
    };
    
    if (solarTerm != null) {
      result['solarTerm'] = solarTerm;
    }
    
    if (festival != null) {
      result['festival'] = festival;
    }
    
    if (lunarFestival != null) {
      result['lunarFestival'] = lunarFestival;
    }
    
    return result;
  }
  
  /// 获取星期名称
  static String _getWeekdayName(int weekday) {
    switch (weekday) {
      case 1:
        return '星期一';
      case 2:
        return '星期二';
      case 3:
        return '星期三';
      case 4:
        return '星期四';
      case 5:
        return '星期五';
      case 6:
        return '星期六';
      case 7:
        return '星期日';
      default:
        return '';
    }
  }
}
