                        -HD:\software\flutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\software\android-sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\software\android-sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\software\android-sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\software\android-sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\project\appProject\ha_smart_home\build\app\intermediates\cxx\RelWithDebInfo\v4l68604\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\project\appProject\ha_smart_home\build\app\intermediates\cxx\RelWithDebInfo\v4l68604\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\project\appProject\ha_smart_home\android\app\.cxx\RelWithDebInfo\v4l68604\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2