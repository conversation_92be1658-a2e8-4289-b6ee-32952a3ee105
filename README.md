# HA Smart Home

一款现代化、精致、可高度自定义的智能家居控制App，基于Flutter开发，用于连接和控制Home Assistant智能家居系统。

## 项目概述

HA Smart Home是一个使用Flutter框架开发的移动应用程序，旨在提供一个美观、流畅且功能丰富的界面来控制您的Home Assistant智能家居系统。

### 主要特性

- **现代化UI设计**：采用Material 3设计语言，支持动态配色和暗黑模式
- **WebSocket实时连接**：与Home Assistant保持实时连接，确保设备状态实时更新
- **自定义卡片布局**：支持自定义首页卡片布局和样式
- **场景管理**：创建和触发自定义场景
- **设备控制**：支持各类智能设备的控制和状态显示
- **数据统计与可视化**：能源消耗统计和设备使用频率分析
- **云同步**：支持配置文件的云备份和恢复

## 技术栈

- **框架**：Flutter
- **状态管理**：GetX
- **网络通信**：Dio + WebSocket
- **本地存储**：GetStorage
- **UI组件**：Material 3
- **动画效果**：Flutter Animate

## 安装与运行

### 开发环境

1. 确保已安装Flutter SDK（版本3.7.0或更高）
2. 克隆仓库：
   ```bash
   git clone https://gitee.com/your-username/ha_smart_home.git
   cd ha_smart_home
   ```
3. 安装依赖：
   ```bash
   flutter pub get
   ```
4. 运行应用：
   ```bash
   flutter run
   ```

### 构建发布版APK

1. 在`android`目录下执行命令生成签名密钥：
   ```bash
   keytool -genkey -v -keystore ha_smart_home.keystore -alias ha_smart_home -keyalg RSA -keysize 2048 -validity 10000
   ```

2. 在`android`目录下创建`key.properties`文件，填入签名信息：
   ```properties
   storePassword=<密钥库密码>
   keyPassword=<密钥密码>
   keyAlias=ha_smart_home
   storeFile=<密钥库文件路径>
   ```

3. 构建APK：
   ```bash
   flutter build apk --release
   ```

4. 构建完成后，APK文件位于：
   ```
   build/app/outputs/flutter-apk/app-release.apk
   ```

## 使用说明

### 登录

1. 启动应用后，输入您的Home Assistant服务器地址（例如：http://number.iepose.cn）
2. 输入长期访问令牌（可在Home Assistant的个人资料页面生成）
3. 点击"登录"按钮

### 首页

- 首页显示您的设备卡片和状态信息
- 可以通过底部导航栏切换到其他页面
- 支持下拉刷新更新设备状态

## 项目结构

```
lib/
├── app/                           # 应用核心配置
│   ├── config/                    # 应用配置
│   ├── controllers/               # 全局控制器
│   ├── data/                      # 数据层
│   └── services/                  # 服务层
├── modules/                       # 功能模块
│   ├── auth/                      # 认证模块
│   ├── home/                      # 首页模块
│   ├── scenes/                    # 场景模块
│   ├── devices/                   # 设备模块
│   └── profile/                   # 设置模块
└── shared/                        # 共享组件
    ├── widgets/                   # 共享小部件
    └── themes/                    # 主题相关
```

## 贡献指南

欢迎贡献代码、报告问题或提出新功能建议！请遵循以下步骤：

1. Fork本仓库
2. 创建您的特性分支：`git checkout -b feature/amazing-feature`
3. 提交您的更改：`git commit -m 'Add some amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 许可证

本项目采用MIT许可证 - 详情请参阅[LICENSE](LICENSE)文件

## 联系方式

- 邮箱：<EMAIL>
- Gitee：[https://gitee.com/your-username/ha_smart_home](https://gitee.com/your-username/ha_smart_home)
