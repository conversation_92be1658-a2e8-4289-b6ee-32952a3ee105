import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../../../app/config/app_constants.dart';

/// 关于页面
class AboutView extends GetView<ProfileController> {
  const AboutView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('关于应用'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 应用图标
          Center(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.home,
                size: 60,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 应用名称
          Center(
            child: Text(
              AppConstants.appName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 应用版本
          Center(
            child: Obx(() => Text(
              '版本: ${controller.appVersion.value ?? 'Unknown'} (${controller.appBuildNumber.value ?? ''})',
              style: const TextStyle(
                fontSize: 16,
              ),
            )),
          ),
          
          const SizedBox(height: 24),
          
          // 应用描述
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '应用简介',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'HA Smart Home是一款基于Home Assistant的智能家居控制应用，提供直观的用户界面和丰富的功能，帮助您轻松管理和控制家中的智能设备。',
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 更新日志
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '更新日志',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildUpdateLogItem(
                    version: '1.0.0',
                    date: '2023-06-01',
                    changes: [
                      '首次发布',
                      '支持Home Assistant WebSocket连接',
                      '支持设备控制和状态显示',
                      '支持场景管理和执行',
                      '支持自定义首页卡片',
                    ],
                  ),
                  _buildUpdateLogItem(
                    version: '1.1.0',
                    date: '2023-07-15',
                    changes: [
                      '添加设备分组功能',
                      '添加设备收藏功能',
                      '添加设备别名设置',
                      '优化WebSocket连接稳定性',
                      '修复已知问题',
                    ],
                  ),
                  _buildUpdateLogItem(
                    version: '1.2.0',
                    date: '2023-09-01',
                    changes: [
                      '添加场景分组功能',
                      '添加场景编辑器',
                      '添加房间管理功能',
                      '优化UI界面',
                      '修复已知问题',
                    ],
                  ),
                  _buildUpdateLogItem(
                    version: '1.3.0',
                    date: '2023-11-15',
                    changes: [
                      '添加数据统计功能',
                      '添加WebDAV云备份功能',
                      '添加UI个性化设置',
                      '优化性能和稳定性',
                      '修复已知问题',
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 开发者信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '开发者信息',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '本应用由Flutter开发，使用GetX状态管理框架，通过WebSocket与Home Assistant通信。',
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Text(
                        '联系邮箱: ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '<EMAIL>',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 版权信息
          Center(
            child: Text(
              '© ${DateTime.now().year} HA Smart Home. All rights reserved.',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建更新日志项
  Widget _buildUpdateLogItem({
    required String version,
    required String date,
    required List<String> changes,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'v$version',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '($date)',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...changes.map((change) => Padding(
            padding: const EdgeInsets.only(left: 16, top: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• '),
                Expanded(child: Text(change)),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
