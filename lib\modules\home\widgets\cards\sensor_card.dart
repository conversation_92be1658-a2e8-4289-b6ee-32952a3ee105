import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/data/models/entity_model.dart';
import '../../../../app/services/ha_service.dart';
import 'base_card.dart';

/// 传感器卡片
class SensorCard extends StatelessWidget {
  /// 卡片ID
  final String id;

  /// 实体ID
  final String entityId;

  /// 卡片样式
  final CardStyle style;

  /// 卡片尺寸
  final CardSize size;

  /// 是否处于编辑模式
  final bool editMode;

  /// 自定义标题
  final String? customTitle;

  /// 是否显示标题栏
  final bool showTitle;

  /// 删除事件
  final VoidCallback? onDelete;

  /// 编辑事件
  final VoidCallback? onEdit;

  const SensorCard({
    super.key,
    required this.id,
    required this.entityId,
    this.style = CardStyle.defaultStyle,
    this.size = CardSize.small,
    this.editMode = false,
    this.customTitle,
    this.showTitle = true,
    this.onDelete,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final haService = Get.find<HaService>();

    return Obx(() {
      final entity = haService.getEntity(entityId);

      if (entity == null) {
        return _buildErrorCard(context);
      }

      return BaseCard(
        id: id,
        title: entity.friendlyName,
        customTitle: customTitle,
        showTitle: showTitle,
        icon: _getSensorIcon(entity),
        style: style,
        size: size,
        editMode: editMode,
        onDelete: onDelete,
        onEdit: onEdit,
        content: _buildSensorContent(context, entity),
      );
    });
  }

  /// 构建传感器内容
  Widget _buildSensorContent(BuildContext context, EntityModel entity) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 传感器图标
        Icon(
          _getSensorIcon(entity),
          size: 36,
          color: _getSensorColor(entity),
        ),

        const SizedBox(height: 16),

        // 传感器值
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              entity.state,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: _getSensorColor(entity),
              ),
            ),
            if (entity.unitOfMeasurement != null) ...[
              const SizedBox(width: 4),
              Text(
                entity.unitOfMeasurement!,
                style: TextStyle(
                  fontSize: 14,
                  color: _getSensorColor(entity),
                ),
              ),
            ],
          ],
        ),

        const SizedBox(height: 8),

        // 最后更新时间
        Text(
          '更新于 ${_formatDateTime(entity.lastUpdated)}',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建错误卡片
  Widget _buildErrorCard(BuildContext context) {
    return BaseCard(
      id: id,
      title: '未找到设备',
      customTitle: customTitle,
      showTitle: showTitle,
      icon: Icons.error,
      style: style,
      size: size,
      editMode: editMode,
      onDelete: onDelete,
      onEdit: onEdit,
      content: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 36,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              '设备不可用',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取传感器图标
  IconData _getSensorIcon(EntityModel entity) {
    // 根据设备类型返回图标
    if (entity.deviceClass != null) {
      switch (entity.deviceClass) {
        case 'temperature':
          return Icons.thermostat;
        case 'humidity':
          return Icons.water_drop;
        case 'pressure':
          return Icons.compress;
        case 'illuminance':
          return Icons.wb_sunny;
        case 'battery':
          return Icons.battery_full;
        case 'power':
          return Icons.power;
        case 'energy':
          return Icons.bolt;
        case 'current':
          return Icons.electric_bolt;
        case 'voltage':
          return Icons.electrical_services;
        case 'gas':
          return Icons.local_fire_department;
        case 'carbon_dioxide':
          return Icons.co2;
        case 'pm25':
          return Icons.air;
        case 'motion':
          return Icons.motion_photos_on;
        case 'door':
          return Icons.door_front_door;
        case 'window':
          return Icons.window;
        case 'moisture':
          return Icons.water_damage;
        case 'smoke':
          return Icons.smoke_free;
        default:
          return Icons.sensors;
      }
    }

    // 根据实体ID前缀判断
    if (entityId.startsWith('sensor.temperature')) {
      return Icons.thermostat;
    } else if (entityId.startsWith('sensor.humidity')) {
      return Icons.water_drop;
    } else if (entityId.startsWith('binary_sensor.motion')) {
      return Icons.motion_photos_on;
    } else if (entityId.startsWith('binary_sensor.door')) {
      return Icons.door_front_door;
    } else if (entityId.startsWith('binary_sensor.window')) {
      return Icons.window;
    }

    // 默认图标
    return Icons.sensors;
  }

  /// 获取传感器颜色
  Color _getSensorColor(EntityModel entity) {
    // 二进制传感器
    if (entity.domain == 'binary_sensor') {
      return entity.isOn ? Colors.red : Colors.green;
    }

    // 根据设备类型返回颜色
    if (entity.deviceClass != null) {
      switch (entity.deviceClass) {
        case 'temperature':
          return Colors.orange;
        case 'humidity':
          return Colors.blue;
        case 'pressure':
          return Colors.purple;
        case 'illuminance':
          return Colors.amber;
        case 'battery':
          return _getBatteryColor(entity.state);
        case 'power':
        case 'energy':
          return Colors.green;
        case 'carbon_dioxide':
        case 'pm25':
          return _getAirQualityColor(entity.state);
        default:
          return Theme.of(Get.context!).colorScheme.primary;
      }
    }

    // 默认颜色
    return Theme.of(Get.context!).colorScheme.primary;
  }

  /// 获取电池颜色
  Color _getBatteryColor(String stateStr) {
    try {
      final state = double.parse(stateStr);
      if (state <= 20) {
        return Colors.red;
      } else if (state <= 50) {
        return Colors.orange;
      } else {
        return Colors.green;
      }
    } catch (e) {
      return Colors.grey;
    }
  }

  /// 获取空气质量颜色
  Color _getAirQualityColor(String stateStr) {
    try {
      final state = double.parse(stateStr);
      if (state <= 50) {
        return Colors.green;
      } else if (state <= 100) {
        return Colors.yellow;
      } else if (state <= 150) {
        return Colors.orange;
      } else {
        return Colors.red;
      }
    } catch (e) {
      return Colors.grey;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inSeconds < 60) {
        return '${difference.inSeconds}秒前';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else {
        return '${difference.inDays}天前';
      }
    } catch (e) {
      return '未知';
    }
  }
}
