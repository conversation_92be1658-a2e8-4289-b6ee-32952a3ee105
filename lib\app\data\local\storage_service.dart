import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../services/log_service.dart';
import 'storage_keys.dart';

/// 本地存储服务
class StorageService extends GetxService {
  // 存储实例
  final GetStorage _storage = GetStorage();
  
  // 单例实例
  static StorageService get to => Get.find<StorageService>();
  
  /// 初始化存储服务
  Future<StorageService> init() async {
    LogService.to.i('初始化存储服务');
    return this;
  }
  
  /// 保存字符串
  Future<void> saveString(String key, String value) async {
    await _storage.write(key, value);
  }
  
  /// 获取字符串
  String? getString(String key) {
    return _storage.read<String>(key);
  }
  
  /// 保存布尔值
  Future<void> saveBool(String key, bool value) async {
    await _storage.write(key, value);
  }
  
  /// 获取布尔值
  bool? getBool(String key) {
    return _storage.read<bool>(key);
  }
  
  /// 保存整数
  Future<void> saveInt(String key, int value) async {
    await _storage.write(key, value);
  }
  
  /// 获取整数
  int? getInt(String key) {
    return _storage.read<int>(key);
  }
  
  /// 保存双精度浮点数
  Future<void> saveDouble(String key, double value) async {
    await _storage.write(key, value);
  }
  
  /// 获取双精度浮点数
  double? getDouble(String key) {
    return _storage.read<double>(key);
  }
  
  /// 保存列表
  Future<void> saveList<T>(String key, List<T> value) async {
    await _storage.write(key, value);
  }
  
  /// 获取列表
  List<T>? getList<T>(String key) {
    final value = _storage.read<List<dynamic>>(key);
    if (value == null) return null;
    return value.cast<T>();
  }
  
  /// 保存Map
  Future<void> saveMap<K, V>(String key, Map<K, V> value) async {
    await _storage.write(key, value);
  }
  
  /// 获取Map
  Map<K, V>? getMap<K, V>(String key) {
    final value = _storage.read<Map<dynamic, dynamic>>(key);
    if (value == null) return null;
    return value.cast<K, V>();
  }
  
  /// 保存JSON对象
  Future<void> saveJson(String key, dynamic value) async {
    final jsonString = jsonEncode(value);
    await _storage.write(key, jsonString);
  }
  
  /// 获取JSON对象
  dynamic getJson(String key) {
    final jsonString = _storage.read<String>(key);
    if (jsonString == null) return null;
    try {
      return jsonDecode(jsonString);
    } catch (e) {
      LogService.to.e('解析JSON失败: $key', e);
      return null;
    }
  }
  
  /// 检查键是否存在
  bool hasKey(String key) {
    return _storage.hasData(key);
  }
  
  /// 删除键
  Future<void> remove(String key) async {
    await _storage.remove(key);
  }
  
  /// 清空所有数据
  Future<void> clear() async {
    await _storage.erase();
  }
}
