/// 场景分组模型
class SceneGroupModel {
  final String id;
  final String name;
  final String? icon;
  final int order;
  
  SceneGroupModel({
    required this.id,
    required this.name,
    this.icon,
    this.order = 0,
  });
  
  /// 从JSON创建
  factory SceneGroupModel.fromJson(Map<String, dynamic> json) {
    return SceneGroupModel(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String?,
      order: json['order'] as int? ?? 0,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'order': order,
    };
  }
  
  /// 创建副本
  SceneGroupModel copyWith({
    String? id,
    String? name,
    String? icon,
    int? order,
  }) {
    return SceneGroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      order: order ?? this.order,
    );
  }
  
  @override
  String toString() {
    return 'SceneGroupModel{id: $id, name: $name}';
  }
}
