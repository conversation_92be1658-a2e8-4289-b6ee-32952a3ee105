import 'package:flutter/material.dart';

/// 属性项
class AttributeItem extends StatelessWidget {
  final String name;
  final String value;
  
  const AttributeItem({
    Key? key,
    required this.name,
    required this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              _formatAttributeName(name),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16.0),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 格式化属性名称
  String _formatAttributeName(String name) {
    // 将下划线替换为空格
    final spacedName = name.replaceAll('_', ' ');
    
    // 将每个单词的首字母大写
    final words = spacedName.split(' ');
    final capitalizedWords = words.map((word) {
      if (word.isEmpty) return '';
      return word[0].toUpperCase() + word.substring(1);
    });
    
    return capitalizedWords.join(' ');
  }
}
