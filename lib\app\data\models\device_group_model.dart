/// 设备分组模型
class DeviceGroupModel {
  final String id;
  final String name;
  final String? icon;
  final List<String> entityIds;
  
  DeviceGroupModel({
    required this.id,
    required this.name,
    this.icon,
    required this.entityIds,
  });
  
  /// 从JSON创建
  factory DeviceGroupModel.fromJson(Map<String, dynamic> json) {
    return DeviceGroupModel(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String?,
      entityIds: (json['entity_ids'] as List<dynamic>).cast<String>(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'entity_ids': entityIds,
    };
  }
  
  /// 创建副本
  DeviceGroupModel copyWith({
    String? id,
    String? name,
    String? icon,
    List<String>? entityIds,
  }) {
    return DeviceGroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      entityIds: entityIds ?? this.entityIds,
    );
  }
}
