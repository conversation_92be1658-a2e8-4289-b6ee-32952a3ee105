/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'HA Smart Home';
  static const String appVersion = '1.0.0';
  
  // API相关
  static const int apiTimeout = 30000; // 毫秒
  static const String apiContentType = 'application/json';
  
  // 存储键
  static const String storageServerUrl = 'server_url';
  static const String storageAccessToken = 'access_token';
  static const String storageRememberMe = 'remember_me';
  static const String storageThemeMode = 'theme_mode';
  
  // WebSocket相关
  static const int wsReconnectInterval = 5000; // 毫秒
  static const int wsMaxReconnectAttempts = 5;
  
  // 其他常量
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);
}
