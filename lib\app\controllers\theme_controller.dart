import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../config/app_constants.dart';

/// 主题控制器
class ThemeController extends GetxController {
  // 主题模式
  final Rx<ThemeMode> themeMode = ThemeMode.system.obs;
  
  // 存储实例
  final GetStorage _storage = GetStorage();
  
  @override
  void onInit() {
    super.onInit();
    _loadThemeMode();
  }
  
  /// 加载主题模式
  void _loadThemeMode() {
    final savedThemeMode = _storage.read<String>(AppConstants.storageThemeMode);
    
    if (savedThemeMode != null) {
      switch (savedThemeMode) {
        case 'light':
          themeMode.value = ThemeMode.light;
          break;
        case 'dark':
          themeMode.value = ThemeMode.dark;
          break;
        default:
          themeMode.value = ThemeMode.system;
          break;
      }
    }
  }
  
  /// 切换主题模式
  void changeThemeMode(ThemeMode mode) {
    themeMode.value = mode;
    _saveThemeMode();
    Get.changeThemeMode(mode);
  }
  
  /// 切换到亮色主题
  void changeToLightTheme() {
    changeThemeMode(ThemeMode.light);
  }
  
  /// 切换到暗色主题
  void changeToDarkTheme() {
    changeThemeMode(ThemeMode.dark);
  }
  
  /// 切换到系统主题
  void changeToSystemTheme() {
    changeThemeMode(ThemeMode.system);
  }
  
  /// 切换主题（亮色/暗色）
  void toggleTheme() {
    if (themeMode.value == ThemeMode.light) {
      changeToDarkTheme();
    } else {
      changeToLightTheme();
    }
  }
  
  /// 保存主题模式
  void _saveThemeMode() {
    String themeModeString;
    
    switch (themeMode.value) {
      case ThemeMode.light:
        themeModeString = 'light';
        break;
      case ThemeMode.dark:
        themeModeString = 'dark';
        break;
      default:
        themeModeString = 'system';
        break;
    }
    
    _storage.write(AppConstants.storageThemeMode, themeModeString);
  }
  
  /// 当前是否是暗色主题
  bool get isDarkMode {
    if (themeMode.value == ThemeMode.system) {
      return Get.mediaQuery.platformBrightness == Brightness.dark;
    }
    return themeMode.value == ThemeMode.dark;
  }
}
