import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/data/models/entity_model.dart';
import '../../controllers/device_detail_controller.dart';

/// 控制面板
class ControlPanel extends StatelessWidget {
  final EntityModel entity;
  final DeviceDetailController controller;
  
  const ControlPanel({
    Key? key,
    required this.entity,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 开关控制
        if (entity.supportsTurnOn) ...[
          _buildSwitchControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 亮度控制
        if (entity.supportsBrightness) ...[
          _buildBrightnessControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 色温控制
        if (entity.supportsColorTemp) ...[
          _buildColorTempControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 颜色控制
        if (entity.supportsColor) ...[
          _buildColorControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 风扇速度控制
        if (entity.supportsFanSpeed) ...[
          _buildFanSpeedControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 温度控制
        if (entity.supportsTemperature) ...[
          _buildTemperatureControl(context),
          const SizedBox(height: 16.0),
        ],
        
        // 湿度控制
        if (entity.supportsHumidity) ...[
          _buildHumidityControl(context),
          const SizedBox(height: 16.0),
        ],
      ],
    );
  }
  
  /// 构建开关控制
  Widget _buildSwitchControl(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '开关',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        Obx(() => Switch(
          value: controller.entity.value?.isOn ?? false,
          onChanged: (_) => controller.toggleEntity(),
        )),
      ],
    );
  }
  
  /// 构建亮度控制
  Widget _buildBrightnessControl(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '亮度',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Obx(() => Text(
              '${((controller.brightness.value / 255) * 100).round()}%',
              style: Theme.of(context).textTheme.bodyMedium,
            )),
          ],
        ),
        const SizedBox(height: 8.0),
        Obx(() => Slider(
          value: controller.brightness.value.toDouble(),
          min: 0,
          max: 255,
          divisions: 25,
          onChanged: (value) {
            controller.brightness.value = value.round();
          },
          onChangeEnd: (value) {
            controller.setBrightness(value.round());
          },
        )),
      ],
    );
  }
  
  /// 构建色温控制
  Widget _buildColorTempControl(BuildContext context) {
    // 获取色温范围
    final minTemp = entity.attributes['min_mireds'] as int? ?? 153;
    final maxTemp = entity.attributes['max_mireds'] as int? ?? 500;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '色温',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Obx(() => Text(
              '${controller.colorTemp.value}',
              style: Theme.of(context).textTheme.bodyMedium,
            )),
          ],
        ),
        const SizedBox(height: 8.0),
        Row(
          children: [
            const Icon(Icons.wb_sunny, color: Colors.amber),
            Expanded(
              child: Obx(() => Slider(
                value: controller.colorTemp.value.toDouble(),
                min: minTemp.toDouble(),
                max: maxTemp.toDouble(),
                divisions: 20,
                onChanged: (value) {
                  controller.colorTemp.value = value.round();
                },
                onChangeEnd: (value) {
                  controller.setColorTemp(value.round());
                },
              )),
            ),
            const Icon(Icons.nights_stay, color: Colors.blue),
          ],
        ),
      ],
    );
  }
  
  /// 构建颜色控制
  Widget _buildColorControl(BuildContext context) {
    // 预定义颜色
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.white,
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '颜色',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8.0),
        SizedBox(
          height: 50.0,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              return Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: InkWell(
                  onTap: () => controller.setColor(color),
                  borderRadius: BorderRadius.circular(25.0),
                  child: Obx(() => Container(
                    width: 50.0,
                    height: 50.0,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: controller.color.value == color
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey,
                        width: controller.color.value == color ? 3.0 : 1.0,
                      ),
                    ),
                  )),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  /// 构建风扇速度控制
  Widget _buildFanSpeedControl(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '风扇速度',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Obx(() => Text(
              '${controller.fanSpeed.value}%',
              style: Theme.of(context).textTheme.bodyMedium,
            )),
          ],
        ),
        const SizedBox(height: 8.0),
        Row(
          children: [
            const Icon(Icons.air, size: 16.0),
            Expanded(
              child: Obx(() => Slider(
                value: controller.fanSpeed.value.toDouble(),
                min: 0,
                max: 100,
                divisions: 10,
                onChanged: (value) {
                  controller.fanSpeed.value = value.round();
                },
                onChangeEnd: (value) {
                  controller.setFanSpeed(value.round());
                },
              )),
            ),
            const Icon(Icons.air, size: 24.0),
          ],
        ),
      ],
    );
  }
  
  /// 构建温度控制
  Widget _buildTemperatureControl(BuildContext context) {
    // 获取温度范围
    final minTemp = entity.attributes['min_temp'] as double? ?? 7.0;
    final maxTemp = entity.attributes['max_temp'] as double? ?? 35.0;
    final step = entity.attributes['target_temp_step'] as double? ?? 0.5;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '温度',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Obx(() => Text(
              '${controller.temperature.value.toStringAsFixed(1)}°C',
              style: Theme.of(context).textTheme.bodyMedium,
            )),
          ],
        ),
        const SizedBox(height: 8.0),
        Row(
          children: [
            const Icon(Icons.ac_unit, color: Colors.blue),
            Expanded(
              child: Obx(() => Slider(
                value: controller.temperature.value,
                min: minTemp,
                max: maxTemp,
                divisions: ((maxTemp - minTemp) / step).round(),
                onChanged: (value) {
                  // 四舍五入到最接近的步长
                  final roundedValue = (value / step).round() * step;
                  controller.temperature.value = roundedValue;
                },
                onChangeEnd: (value) {
                  // 四舍五入到最接近的步长
                  final roundedValue = (value / step).round() * step;
                  controller.setTemperature(roundedValue);
                },
              )),
            ),
            const Icon(Icons.whatshot, color: Colors.red),
          ],
        ),
      ],
    );
  }
  
  /// 构建湿度控制
  Widget _buildHumidityControl(BuildContext context) {
    // 获取湿度范围
    final minHumidity = entity.attributes['min_humidity'] as int? ?? 30;
    final maxHumidity = entity.attributes['max_humidity'] as int? ?? 80;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '湿度',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Obx(() => Text(
              '${controller.humidity.value}%',
              style: Theme.of(context).textTheme.bodyMedium,
            )),
          ],
        ),
        const SizedBox(height: 8.0),
        Row(
          children: [
            const Icon(Icons.water_drop_outlined, color: Colors.blue),
            Expanded(
              child: Obx(() => Slider(
                value: controller.humidity.value.toDouble(),
                min: minHumidity.toDouble(),
                max: maxHumidity.toDouble(),
                divisions: maxHumidity - minHumidity,
                onChanged: (value) {
                  controller.humidity.value = value.round();
                },
                onChangeEnd: (value) {
                  controller.setHumidity(value.round());
                },
              )),
            ),
            const Icon(Icons.water_drop, color: Colors.blue),
          ],
        ),
      ],
    );
  }
}
