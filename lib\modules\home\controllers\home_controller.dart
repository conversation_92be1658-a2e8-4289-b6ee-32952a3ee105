import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../../app/services/auth_service.dart';
import '../../../app/services/ha_service.dart';
import '../../../app/services/log_service.dart';
import '../../../app/services/rest_api_service.dart';
import '../../../app/services/websocket_service.dart';
import '../controllers/card_editor_controller.dart';

/// 房间模型
class RoomModel {
  /// 房间ID
  final String id;

  /// 房间名称
  final String name;

  /// 房间类型
  final String type;

  /// 房间图标
  final String? icon;

  /// 房间内的设备ID列表
  final List<String> entityIds;

  RoomModel({
    required this.id,
    required this.name,
    required this.type,
    this.icon,
    required this.entityIds,
  });

  /// 从JSON创建
  factory RoomModel.fromJson(Map<String, dynamic> json) {
    return RoomModel(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      icon: json['icon'] as String?,
      entityIds: (json['entity_ids'] as List<dynamic>).cast<String>(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'icon': icon,
      'entity_ids': entityIds,
    };
  }
}

/// 首页控制器
class HomeController extends GetxController {
  // 服务
  final AuthService _authService = Get.find<AuthService>();
  final WebSocketService _wsService = Get.find<WebSocketService>();
  final HaService _haService = Get.find<HaService>();

  // 存储实例
  final GetStorage _storage = GetStorage();

  // Home Assistant版本
  final Rx<String?> haVersion = Rx<String?>(null);

  // WebSocket连接状态
  final RxBool isConnected = false.obs;

  // 页面加载状态
  final RxBool isPageLoaded = false.obs;

  // 房间列表
  final RxList<RoomModel> rooms = <RoomModel>[].obs;

  // 当前选中的房间
  final Rx<RoomModel?> selectedRoom = Rx<RoomModel?>(null);

  // 是否处于编辑模式
  final RxBool isEditMode = false.obs;

  @override
  void onInit() {
    super.onInit();

    // 获取Home Assistant版本
    haVersion.value = _authService.haVersion.value;

    // 监听WebSocket连接状态
    _wsService.isConnected.listen((connected) {
      isConnected.value = connected;

      // 如果页面已加载且WebSocket已连接，加载设备信息
      if (isPageLoaded.value && connected && _haService.entities.isEmpty) {
        _loadEntities();
      }
    });

    // 监听Home Assistant版本变化
    _authService.haVersion.listen((version) {
      haVersion.value = version;
    });

    // 在下一帧渲染完成后标记页面已加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      isPageLoaded.value = true;
      LogService.to.i('首页加载完成');

      // 如果WebSocket已连接，加载设备信息
      if (_wsService.isConnected.value && _haService.entities.isEmpty) {
        _loadEntities();
      }
    });

    // 加载房间列表
    _loadRooms();

    // 默认选择"全部房间"（selectedRoom.value = null）
    // 不再自动选择第一个房间
    selectRoom(null);
    LogService.to.i('默认选择全部房间');
  }

  /// 加载设备信息
  void _loadEntities() {
    LogService.to.i('首页加载完成后开始加载设备信息');
    _haService.loadInitialEntities();
  }

  /// 加载房间列表
  void _loadRooms() {
    try {
      final savedRooms = _storage.read<List<dynamic>>('home_rooms');

      if (savedRooms != null) {
        rooms.value = savedRooms
            .map((item) => RoomModel.fromJson(item as Map<String, dynamic>))
            .toList();

        LogService.to.i('已加载 ${rooms.length} 个房间');
      } else {
        // 创建默认房间
        _createDefaultRooms();
      }

      // 不再自动选择第一个房间，保持全部房间的选择
    } catch (e) {
      LogService.to.e('加载房间失败', e);
      // 创建默认房间
      _createDefaultRooms();

      // 不再自动选择第一个房间，保持全部房间的选择
    }
  }

  /// 创建默认房间
  void _createDefaultRooms() {
    LogService.to.i('创建默认房间');

    rooms.clear();

    // 添加默认房间
    rooms.addAll([
      RoomModel(
        id: 'living_room',
        name: '客厅',
        type: 'living_room',
        icon: 'weekend',
        entityIds: [],
      ),
      RoomModel(
        id: 'bedroom',
        name: '卧室',
        type: 'bedroom',
        icon: 'bed',
        entityIds: [],
      ),
      RoomModel(
        id: 'kitchen',
        name: '厨房',
        type: 'kitchen',
        icon: 'kitchen',
        entityIds: [],
      ),
      RoomModel(
        id: 'bathroom',
        name: '浴室',
        type: 'bathroom',
        icon: 'bathtub',
        entityIds: [],
      ),
    ]);

    // 保存房间
    _saveRooms();

    // 不再自动选择第一个房间，保持全部房间的选择
  }

  /// 保存房间
  void _saveRooms() {
    try {
      final roomsJson = rooms.map((room) => room.toJson()).toList();
      _storage.write('home_rooms', roomsJson);
      LogService.to.i('已保存 ${rooms.length} 个房间');
    } catch (e) {
      LogService.to.e('保存房间失败', e);
    }
  }

  /// 选择房间
  void selectRoom(RoomModel? room) {
    selectedRoom.value = room;

    // 同步更新CardEditorController的selectedRoomId
    if (Get.isRegistered<CardEditorController>()) {
      final cardEditorController = Get.find<CardEditorController>();
      cardEditorController.setRoomId(room?.id);
    }

    LogService.to.i('已选择房间: ${room?.name ?? '全部房间'}');
  }

  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode.value = !isEditMode.value;
    LogService.to.i('编辑模式: ${isEditMode.value}');
  }

  /// 添加房间
  void addRoom(RoomModel room) {
    // 检查是否已存在相同ID的房间
    final existingIndex = rooms.indexWhere((r) => r.id == room.id);
    if (existingIndex != -1) {
      // 如果存在，更新房间
      rooms[existingIndex] = room;
      LogService.to.i('已更新房间: ${room.name}');
    } else {
      // 如果不存在，添加房间
      rooms.add(room);
      LogService.to.i('已添加房间: ${room.name}');
    }

    // 保存房间
    _saveRooms();
  }

  /// 删除房间
  void deleteRoom(String roomId) {
    // 如果当前选中的房间被删除，清除选择
    if (selectedRoom.value?.id == roomId) {
      selectedRoom.value = null;
    }

    // 删除房间
    rooms.removeWhere((room) => room.id == roomId);
    LogService.to.i('已删除房间: $roomId');

    // 保存房间
    _saveRooms();
  }

  /// 更新房间
  void updateRoom(RoomModel room) {
    // 查找房间索引
    final index = rooms.indexWhere((r) => r.id == room.id);

    if (index != -1) {
      // 更新房间
      rooms[index] = room;

      // 如果当前选中的房间被更新，更新选择
      if (selectedRoom.value?.id == room.id) {
        selectedRoom.value = room;
      }

      LogService.to.i('已更新房间: ${room.name}');

      // 保存房间
      _saveRooms();
    }
  }

  /// 更新房间顺序
  void updateRoomOrder(List<RoomModel> newOrder) {
    // 更新房间列表
    rooms.value = newOrder;
    LogService.to.i('已更新房间顺序');

    // 保存房间
    _saveRooms();
  }

  /// 生成唯一房间ID
  String generateRoomId(String type) {
    return '${type}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认退出'),
          content: const Text('确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        LogService.to.i('用户确认退出登录');

        // 执行退出登录
        await _authService.logout();

        LogService.to.i('退出登录成功，正在导航到登录页面');

        // 导航到登录页面，清除所有页面历史
        Get.offAllNamed('/login', predicate: (_) => false);
      } else {
        LogService.to.i('用户取消退出登录');
      }
    } catch (e) {
      LogService.to.e('退出登录失败', e);
      Get.snackbar(
        '退出失败',
        '退出登录失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
