import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/scenes_controller.dart';
import '../widgets/scene_card.dart';
import '../../../app/config/app_routes.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../../../shared/widgets/custom_bottom_nav.dart';

/// 场景页面
class ScenesView extends GetView<ScenesController> {
  const ScenesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('场景'),
        actions: [
          // 搜索按钮
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _showSearchDialog(context);
            },
            tooltip: '搜索场景',
          ),

          // 收藏过滤按钮
          Obx(() => IconButton(
            icon: Icon(
              controller.showFavorites.value
                  ? Icons.favorite
                  : Icons.favorite_border,
              color: controller.showFavorites.value ? Colors.red : null,
            ),
            onPressed: controller.toggleShowFavorites,
            tooltip: controller.showFavorites.value ? '显示全部' : '只看收藏',
          )),

          // 更多菜单
          PopupMenuButton(
            icon: const Icon(Icons.more_vert),
            tooltip: '更多选项',
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'manage_groups',
                child: Row(
                  children: [
                    Icon(Icons.folder),
                    SizedBox(width: 8),
                    Text('管理分组'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort',
                child: Row(
                  children: [
                    Icon(Icons.sort),
                    SizedBox(width: 8),
                    Text('排序方式'),
                  ],
                ),
              ),
            ],
            onSelected: (value) {
              if (value == 'manage_groups') {
                Get.toNamed(AppRoutes.sceneGroups);
              } else if (value == 'sort') {
                _showSortDialog(context);
              }
            },
          ),
        ],
      ),

      // 场景列表
      body: Column(
        children: [
          // 分组选择器
          _buildGroupSelector(),

          // 搜索过滤提示
          Obx(() => controller.searchQuery.value.isNotEmpty
              ? Container(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '搜索: "${controller.searchQuery.value}"',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: controller.clearSearch,
                        tooltip: '清除搜索',
                        iconSize: 20,
                      ),
                    ],
                  ),
                )
              : const SizedBox.shrink()),

          // 场景列表
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: LoadingIndicator());
              }

              if (controller.errorMessage.value != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        controller.errorMessage.value!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.loadScenes,
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                );
              }

              final filteredScenes = controller.filteredScenes;

              if (filteredScenes.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        controller.searchQuery.value.isNotEmpty
                            ? Icons.search_off
                            : controller.showFavorites.value
                                ? Icons.favorite_border
                                : Icons.auto_awesome_outlined,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.searchQuery.value.isNotEmpty
                            ? '没有找到匹配的场景'
                            : controller.showFavorites.value
                                ? '没有收藏的场景'
                                : controller.selectedGroupId.value != 'all'
                                    ? '该分组下没有场景'
                                    : '没有场景',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      if (controller.searchQuery.value.isNotEmpty)
                        ElevatedButton(
                          onPressed: controller.clearSearch,
                          child: const Text('清除搜索'),
                        )
                      else if (controller.showFavorites.value)
                        ElevatedButton(
                          onPressed: controller.toggleShowFavorites,
                          child: const Text('显示全部场景'),
                        )
                      else if (controller.selectedGroupId.value != 'all')
                        ElevatedButton(
                          onPressed: () => controller.setSelectedGroup('all'),
                          child: const Text('显示全部场景'),
                        )
                      else
                        ElevatedButton(
                          onPressed: () => Get.toNamed(AppRoutes.sceneEditor),
                          child: const Text('创建场景'),
                        ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.loadScenes,
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: filteredScenes.length,
                  itemBuilder: (context, index) {
                    final scene = filteredScenes[index];
                    return SceneCard(
                      scene: scene,
                      onTap: () => controller.activateScene(scene),
                      onEdit: scene.isCustom
                          ? () => Get.toNamed(
                                AppRoutes.sceneEditor,
                                arguments: scene,
                              )
                          : null,
                      onDelete: scene.isCustom
                          ? () => _showDeleteConfirmDialog(context, scene)
                          : null,
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),

      // 悬浮按钮 - 添加场景
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.toNamed(AppRoutes.sceneEditor),
        tooltip: '添加场景',
        child: const Icon(Icons.add),
      ),

      // 在主容器中使用，不需要底部导航栏
      // bottomNavigationBar: const CustomBottomNav(
      //   currentIndex: 1,
      // ),
    );
  }

  /// 构建分组选择器
  Widget _buildGroupSelector() {
    return Obx(() => Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        children: [
          // 全部分组选项
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ChoiceChip(
              label: const Text('全部'),
              selected: controller.selectedGroupId.value == 'all',
              onSelected: (selected) {
                if (selected) {
                  controller.setSelectedGroup('all');
                }
              },
            ),
          ),

          // 未分组选项
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ChoiceChip(
              label: const Text('未分组'),
              selected: controller.selectedGroupId.value == 'ungrouped',
              onSelected: (selected) {
                if (selected) {
                  controller.setSelectedGroup('ungrouped');
                }
              },
            ),
          ),

          // 分组列表
          ...controller.sceneGroups.map((group) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ChoiceChip(
              label: Text(group.name),
              selected: controller.selectedGroupId.value == group.id,
              onSelected: (selected) {
                if (selected) {
                  controller.setSelectedGroup(group.id);
                }
              },
              avatar: group.icon != null
                  ? Icon(
                      _getIconData(group.icon!),
                      size: 18,
                    )
                  : null,
            ),
          )),
        ],
      ),
    ));
  }

  /// 显示搜索对话框
  void _showSearchDialog(BuildContext context) {
    final searchController = TextEditingController(text: controller.searchQuery.value);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索场景'),
        content: TextField(
          controller: searchController,
          decoration: const InputDecoration(
            hintText: '输入场景名称或ID',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onSubmitted: (value) {
            controller.setSearchQuery(value);
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              controller.setSearchQuery(searchController.text);
              Navigator.pop(context);
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  /// 显示排序对话框
  void _showSortDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('排序方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('按名称'),
              leading: const Icon(Icons.sort_by_alpha),
              onTap: () {
                controller.scenes.sort((a, b) => a.name.compareTo(b.name));
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('按创建时间'),
              leading: const Icon(Icons.access_time),
              onTap: () {
                controller.scenes.sort((a, b) => a.id.compareTo(b.id));
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('按设备数量'),
              leading: const Icon(Icons.devices),
              onTap: () {
                controller.scenes.sort((a, b) => b.entityIds.length.compareTo(a.entityIds.length));
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, dynamic scene) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除场景'),
        content: Text('确定要删除场景"${scene.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              controller.deleteScene(scene);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 获取图标数据
  IconData _getIconData(String iconName) {
    // 常用图标映射
    final iconMap = {
      'home': Icons.home,
      'lightbulb': Icons.lightbulb,
      'tv': Icons.tv,
      'bed': Icons.bed,
      'movie': Icons.movie,
      'music_note': Icons.music_note,
      'restaurant': Icons.restaurant,
      'local_cafe': Icons.local_cafe,
      'wb_sunny': Icons.wb_sunny,
      'nightlight': Icons.nightlight,
      'auto_awesome': Icons.auto_awesome,
      'celebration': Icons.celebration,
      'weekend': Icons.weekend,
      'sports_esports': Icons.sports_esports,
      'fitness_center': Icons.fitness_center,
      'spa': Icons.spa,
      'pool': Icons.pool,
      'shower': Icons.shower,
      'local_bar': Icons.local_bar,
      'local_dining': Icons.local_dining,
      'folder': Icons.folder,
    };

    return iconMap[iconName] ?? Icons.auto_awesome;
  }
}