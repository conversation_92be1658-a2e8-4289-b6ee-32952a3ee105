import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/local/storage_keys.dart';
import '../../../app/data/local/storage_service.dart';
import '../../../app/data/models/device_group_model.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../app/services/ha_service.dart';
import '../../../app/services/websocket_service.dart';
import '../../../app/services/log_service.dart';

/// 设备列表控制器
class DevicesController extends GetxController with GetTickerProviderStateMixin {
  // 服务
  final HaService haService = Get.find<HaService>();
  final WebSocketService wsService = Get.find<WebSocketService>();
  final StorageService _storageService = Get.find<StorageService>();

  // 加载状态
  final RxBool isLoading = false.obs;

  // 数据是否已准备好
  final RxBool isDataReady = false.obs;

  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);

  // 搜索关键字
  final RxString searchQuery = ''.obs;

  // 最后一次刷新时间
  DateTime _lastRefreshTime = DateTime.now().subtract(const Duration(seconds: 30));

  // 选中的域名
  final RxString selectedDomain = 'all'.obs;

  // 选中的排序方式
  final RxString sortBy = 'domain'.obs;

  // 标签控制器
  late TabController tabController;

  // 设备分组
  final RxList<DeviceGroupModel> deviceGroups = <DeviceGroupModel>[].obs;

  // 收藏的设备
  final RxList<String> favoriteDevices = <String>[].obs;

  // 设备别名 (entityId -> 别名)
  final RxMap<String, String> deviceAliases = <String, String>{}.obs;

  // 当前选中的分组ID
  final RxString selectedGroupId = 'all'.obs;

  // 是否显示收藏设备
  final RxBool showFavorites = false.obs;

  // 域名列表
  final RxList<String> domains = <String>[].obs;

  // 过滤后的实体列表
  List<EntityModel> get filteredEntities {
    final query = searchQuery.value.toLowerCase();
    final entities = haService.entities.toList(); // 转换为普通List
    List<EntityModel> result = [];

    // 按分组过滤
    if (selectedGroupId.value != 'all') {
      // 查找选中的分组
      final selectedGroup = deviceGroups.firstWhereOrNull((group) => group.id == selectedGroupId.value);
      if (selectedGroup != null) {
        // 获取分组中的实体
        result = entities.where((entity) => selectedGroup.entityIds.contains(entity.entityId)).toList();
      } else {
        // 如果找不到分组，重置为全部
        selectedGroupId.value = 'all';
        result = entities;
      }
    }
    // 按收藏过滤
    else if (showFavorites.value) {
      result = entities.where((entity) => favoriteDevices.contains(entity.entityId)).toList();
    }
    // 按域名过滤
    else if (selectedDomain.value != 'all') {
      result = entities.where((entity) => entity.domain == selectedDomain.value).toList();
    } else {
      result = entities;
    }

    // 按搜索关键字过滤
    if (query.isNotEmpty) {
      result = result.where((entity) {
        // 获取设备别名
        final alias = deviceAliases[entity.entityId];

        return entity.entityId.toLowerCase().contains(query) ||
               entity.friendlyName.toLowerCase().contains(query) ||
               (alias != null && alias.toLowerCase().contains(query));
      }).toList();
    }

    // 按选定方式排序
    switch (sortBy.value) {
      case 'name':
        result.sort((a, b) {
          // 使用别名进行排序
          final aName = deviceAliases[a.entityId] ?? a.friendlyName;
          final bName = deviceAliases[b.entityId] ?? b.friendlyName;
          return aName.compareTo(bName);
        });
        break;
      case 'state':
        result.sort((a, b) => a.state.compareTo(b.state));
        break;
      case 'favorite':
        // 收藏的排在前面
        result.sort((a, b) {
          final aIsFavorite = favoriteDevices.contains(a.entityId);
          final bIsFavorite = favoriteDevices.contains(b.entityId);
          if (aIsFavorite && !bIsFavorite) return -1;
          if (!aIsFavorite && bIsFavorite) return 1;

          // 如果收藏状态相同，按名称排序
          final aName = deviceAliases[a.entityId] ?? a.friendlyName;
          final bName = deviceAliases[b.entityId] ?? b.friendlyName;
          return aName.compareTo(bName);
        });
        break;
      case 'domain':
      default:
        result.sort((a, b) {
          final domainCompare = a.domain.compareTo(b.domain);
          if (domainCompare != 0) return domainCompare;

          // 使用别名进行排序
          final aName = deviceAliases[a.entityId] ?? a.friendlyName;
          final bName = deviceAliases[b.entityId] ?? b.friendlyName;
          return aName.compareTo(bName);
        });
        break;
    }

    return result;
  }

  // 按域名分组的实体
  Map<String, List<EntityModel>> get entitiesByDomain {
    final result = <String, List<EntityModel>>{};

    // 如果显示收藏，则只显示收藏的设备
    if (showFavorites.value) {
      final favoriteEntities = haService.entities
          .where((entity) => favoriteDevices.contains(entity.entityId))
          .toList();

      // 按域名分组
      for (final domain in domains) {
        if (domain == 'all') continue;

        final domainEntities = favoriteEntities
            .where((entity) => entity.domain == domain)
            .toList();

        if (domainEntities.isNotEmpty) {
          result[domain] = domainEntities;
        }
      }
    }
    // 如果选择了分组，则只显示分组中的设备
    else if (selectedGroupId.value != 'all') {
      final selectedGroup = deviceGroups.firstWhereOrNull((group) => group.id == selectedGroupId.value);
      if (selectedGroup != null) {
        final groupEntities = haService.entities
            .where((entity) => selectedGroup.entityIds.contains(entity.entityId))
            .toList();

        // 按域名分组
        for (final domain in domains) {
          if (domain == 'all') continue;

          final domainEntities = groupEntities
              .where((entity) => entity.domain == domain)
              .toList();

          if (domainEntities.isNotEmpty) {
            result[domain] = domainEntities;
          }
        }
      }
    }
    // 否则显示所有设备
    else {
      for (final domain in domains) {
        if (domain == 'all') continue;

        final entities = haService.entities.where((entity) => entity.domain == domain).toList();

        // 按搜索关键字过滤
        final query = searchQuery.value.toLowerCase();
        if (query.isNotEmpty) {
          result[domain] = entities.where((entity) {
            // 获取设备别名
            final alias = deviceAliases[entity.entityId];

            return entity.entityId.toLowerCase().contains(query) ||
                   entity.friendlyName.toLowerCase().contains(query) ||
                   (alias != null && alias.toLowerCase().contains(query));
          }).toList();
        } else {
          result[domain] = entities;
        }
      }
    }

    // 对所有域名的实体进行排序
    for (final domain in result.keys) {
      // 按名称排序（使用别名）
      result[domain]!.sort((a, b) {
        final aName = deviceAliases[a.entityId] ?? a.friendlyName;
        final bName = deviceAliases[b.entityId] ?? b.friendlyName;
        return aName.compareTo(bName);
      });
    }

    return result;
  }

  @override
  void onInit() {
    super.onInit();

    // 初始化域名列表和标签控制器
    domains.add('all');
    tabController = TabController(length: domains.length, vsync: this);

    // 加载设备分组、收藏和别名
    _loadDeviceGroups();
    _loadFavoriteDevices();
    _loadDeviceAliases();

    // 监听WebSocket连接状态
    wsService.isConnected.listen((connected) {
      if (connected) {
        // 只有在WebSocket重新连接时才刷新数据
        LogService.to.i('WebSocket已连接，刷新实体列表');
        fetchEntities(forceRefresh: true);
      }
    });

    // 不再监听实体列表的每次变化，只在特定情况下更新

    // 如果已经有实体数据，直接更新UI
    if (haService.entities.isNotEmpty) {
      LogService.to.i('使用现有实体数据，数量: ${haService.entities.length}');
      updateDomains();
    } else {
      // 如果没有实体数据，尝试获取
      fetchEntities(forceRefresh: false);
    }
  }

  /// 加载设备分组
  void _loadDeviceGroups() {
    try {
      final groupsJson = _storageService.getJson(StorageKeys.deviceGroups);
      if (groupsJson != null) {
        final List<dynamic> groupsList = groupsJson as List<dynamic>;
        deviceGroups.value = groupsList
            .map((json) => DeviceGroupModel.fromJson(json as Map<String, dynamic>))
            .toList();
        LogService.to.i('加载了 ${deviceGroups.length} 个设备分组');
      } else {
        LogService.to.d('没有保存的设备分组');
      }
    } catch (e) {
      LogService.to.e('加载设备分组失败', e);
    }
  }

  /// 加载收藏设备
  void _loadFavoriteDevices() {
    try {
      final favorites = _storageService.getList<String>(StorageKeys.favoriteDevices);
      if (favorites != null) {
        favoriteDevices.value = favorites;
        LogService.to.i('加载了 ${favoriteDevices.length} 个收藏设备');
      } else {
        LogService.to.d('没有保存的收藏设备');
      }
    } catch (e) {
      LogService.to.e('加载收藏设备失败', e);
    }
  }

  /// 加载设备别名
  void _loadDeviceAliases() {
    try {
      final aliases = _storageService.getMap<String, String>(StorageKeys.deviceAliases);
      if (aliases != null) {
        deviceAliases.value = aliases;
        LogService.to.i('加载了 ${deviceAliases.length} 个设备别名');
      } else {
        LogService.to.d('没有保存的设备别名');
      }
    } catch (e) {
      LogService.to.e('加载设备别名失败', e);
    }
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// 获取实体列表
  /// [forceRefresh] 是否强制刷新数据（点击刷新按钮时使用）
  Future<void> fetchEntities({int retryCount = 0, bool forceRefresh = false}) async {
    if (!wsService.isConnected.value) {
      LogService.to.w('未连接到Home Assistant，无法获取实时数据');

      // 检查是否有缓存数据
      if (haService.entities.isNotEmpty) {
        if (forceRefresh) {
          // 如果是用户点击刷新按钮，显示网络断开提示
          Get.snackbar(
            '网络已断开',
            '当前显示的是缓存数据，请检查网络连接后再刷新',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }

        // 标记为使用缓存数据
        if (!haService.isUsingCachedData.value) {
          haService.isUsingCachedData.value = true;
        }

        // 更新域名列表
        updateDomains();
        update();
        return;
      }

      // 如果没有缓存数据，显示错误信息
      errorMessage.value = '未连接到Home Assistant，请检查网络连接';
      return;
    }

    // 检查是否需要刷新
    final now = DateTime.now();
    final timeSinceLastRefresh = now.difference(_lastRefreshTime);

    // 如果已经有实体数据且不是强制刷新，且距离上次刷新时间不超过5秒，直接更新UI
    if (haService.entities.isNotEmpty && !forceRefresh && timeSinceLastRefresh.inSeconds < 5) {
      LogService.to.d('使用现有实体数据，数量: ${haService.entities.length}，距离上次刷新: ${timeSinceLastRefresh.inSeconds}秒');
      updateDomains();
      update();
      return;
    }

    // 更新最后刷新时间
    _lastRefreshTime = now;

    // 记录当前TabController状态，用于后续检查
    final oldTabLength = tabController.length;
    final oldDomainsLength = domains.length;
    LogService.to.d('刷新前状态: TabController长度=$oldTabLength, 域名数量=$oldDomainsLength');

    // 设置加载状态
    isLoading.value = true;
    errorMessage.value = null;

    // 使用微任务延迟，让UI先更新加载状态
    Future.microtask(() async {
      try {
        LogService.to.i('开始获取实体列表...');
        LogService.to.i('开始获取实体数据，强制刷新: $forceRefresh');

        // 记录刷新前的实体数量
        final beforeCount = haService.entities.length;
        LogService.to.d('刷新前实体数量: $beforeCount');

        // 如果是强制刷新，先同步实体映射到列表
        if (forceRefresh) {
          LogService.to.i('强制刷新，同步实体映射到列表');
          haService.syncEntitiesToList();
        }

        // 将forceRefresh参数传递给HaService
        // 注意：即使是强制刷新，也不要清空现有实体列表，以避免UI问题
        final entities = await haService.fetchAllEntities(forceRefresh: forceRefresh);

        // 检查HaService中的实体列表，而不是返回值
        final haEntities = haService.entities;

        // 记录刷新后的实体数量
        final afterCount = haEntities.length;
        LogService.to.d('刷新后实体数量: $afterCount');

        // 如果获取到了实体数据，记录日志
        if (entities.isNotEmpty) {
          LogService.to.i('获取到 ${entities.length} 个实体');
          LogService.to.d('HaService中的实体数量: ${haEntities.length}');
        } else if (haEntities.isNotEmpty) {
          // 如果返回值为空但HaService中有数据，使用现有数据
          LogService.to.w('获取到0个实体，但HaService中有 ${haEntities.length} 个实体，使用现有数据');
        } else {
          // 如果两者都为空，记录警告
          LogService.to.w('警告：获取到0个实体，HaService中也没有实体数据');
        }

        // 检查实体数量是否变化
        if (beforeCount != afterCount) {
          LogService.to.i('实体数量已变化: $beforeCount -> $afterCount');
        } else {
          LogService.to.d('实体数量未变化: $beforeCount');
        }

        // 更新域名列表
        updateDomains();

        // 标记数据已准备好
        isDataReady.value = true;

        // 检查TabController是否需要更新
        if (forceRefresh || tabController.length != domains.length) {
          LogService.to.i('需要更新TabController: 强制刷新=$forceRefresh, TabController长度=${tabController.length}, 域名数量=${domains.length}');

          // 在主线程中安排TabController更新
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // 再次检查，确保仍然需要更新
            if (tabController.length != domains.length) {
              LogService.to.i('在下一帧中更新TabController: 当前长度=${tabController.length}, 目标长度=${domains.length}');
              recreateTabController();
            }
          });
        }

        // 强制更新UI
        update();

        // 使用HaService中的实体列表来判断是否为空
        if (haEntities.isEmpty && retryCount < 2) {
          LogService.to.w('警告：HaService中的实体列表为空，尝试重试 (${retryCount + 1}/2)');
          // 延迟3秒后重试
          await Future.delayed(const Duration(seconds: 3));
          return fetchEntities(retryCount: retryCount + 1, forceRefresh: forceRefresh);
        } else if (haEntities.isEmpty) {
          LogService.to.w('警告：HaService中的实体列表为空，已达到最大重试次数');
          errorMessage.value = '未获取到任何设备';
        }
      } catch (e, stackTrace) {
        LogService.to.e('获取实体列表失败', e, stackTrace);

        // 检查是否仍然有实体数据
        if (haService.entities.isNotEmpty) {
          LogService.to.i('虽然获取失败，但仍有 ${haService.entities.length} 个实体，继续使用现有数据');
          // 更新域名列表
          updateDomains();
          // 检查TabController是否需要更新
          if (tabController.length != domains.length) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              recreateTabController();
            });
          }
        } else if (retryCount < 2) {
          LogService.to.i('尝试重试获取实体列表 (${retryCount + 1}/2)');
          // 延迟3秒后重试
          await Future.delayed(const Duration(seconds: 3));
          return fetchEntities(retryCount: retryCount + 1, forceRefresh: forceRefresh);
        } else {
          errorMessage.value = '获取设备列表失败: $e';
        }
      } finally {
        isLoading.value = false;

        // 记录最终状态
        LogService.to.d('刷新后状态: TabController长度=${tabController.length}, 域名数量=${domains.length}');

        // 如果TabController长度与域名数量不匹配，记录警告
        if (tabController.length != domains.length) {
          LogService.to.w('警告：刷新后TabController长度(${tabController.length})与域名数量(${domains.length})不匹配');
        }
      }
    });
  }

  /// 更新域名列表
  void updateDomains() {
    final domainSet = <String>{'all'};

    for (final entity in haService.entities) {
      domainSet.add(entity.domain);
    }

    final newDomains = domainSet.toList()..sort();
    LogService.to.d('更新域名列表: $newDomains');

    // 检查是否有变化
    final hasChanged = domains.length != newDomains.length ||
                      !domains.toSet().containsAll(newDomains.toSet());

    // 更新域名列表
    domains.value = newDomains;

    // 如果域名列表有变化，安排在下一帧更新TabController
    if (hasChanged) {
      LogService.to.i('域名列表已变化，安排更新TabController');

      // 使用延迟执行，确保域名列表更新后再更新TabController
      Future.delayed(Duration.zero, () {
        recreateTabController();
      });
    }
  }

  /// 切换实体状态
  Future<void> toggleEntity(EntityModel entity) async {
    try {
      if (entity.isOn) {
        await haService.turnOff(entity.entityId);
      } else {
        await haService.turnOn(entity.entityId);
      }
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法切换 ${entity.friendlyName} 的状态: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 设置搜索关键字
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// 设置选中的域名
  void setSelectedDomain(String domain) {
    selectedDomain.value = domain;
  }

  /// 设置排序方式
  void setSortBy(String sort) {
    sortBy.value = sort;
  }

  /// 清除搜索
  void clearSearch() {
    searchQuery.value = '';
  }

  /// 添加设备分组
  Future<void> addDeviceGroup(String name, String? icon, List<String> entityIds) async {
    try {
      // 生成唯一ID
      final id = 'group_${DateTime.now().millisecondsSinceEpoch}';

      // 创建分组
      final group = DeviceGroupModel(
        id: id,
        name: name,
        icon: icon,
        entityIds: entityIds,
      );

      // 添加到列表
      deviceGroups.add(group);

      // 保存到存储
      await _saveDeviceGroups();

      // 显示成功消息
      Get.snackbar(
        '成功',
        '已创建设备分组: $name',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('添加设备分组失败', e);
      Get.snackbar(
        '失败',
        '创建设备分组失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 更新设备分组
  Future<void> updateDeviceGroup(String id, String name, String? icon, List<String> entityIds) async {
    try {
      // 查找分组索引
      final index = deviceGroups.indexWhere((group) => group.id == id);
      if (index == -1) {
        throw Exception('未找到设备分组');
      }

      // 更新分组
      final updatedGroup = deviceGroups[index].copyWith(
        name: name,
        icon: icon,
        entityIds: entityIds,
      );

      // 更新列表
      deviceGroups[index] = updatedGroup;

      // 保存到存储
      await _saveDeviceGroups();

      // 显示成功消息
      Get.snackbar(
        '成功',
        '已更新设备分组: $name',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('更新设备分组失败', e);
      Get.snackbar(
        '失败',
        '更新设备分组失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 删除设备分组
  Future<void> deleteDeviceGroup(String id) async {
    try {
      // 查找分组
      final group = deviceGroups.firstWhere((group) => group.id == id);

      // 从列表中移除
      deviceGroups.removeWhere((group) => group.id == id);

      // 如果当前选中的是被删除的分组，切换到全部
      if (selectedGroupId.value == id) {
        selectedGroupId.value = 'all';
      }

      // 保存到存储
      await _saveDeviceGroups();

      // 显示成功消息
      Get.snackbar(
        '成功',
        '已删除设备分组: ${group.name}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('删除设备分组失败', e);
      Get.snackbar(
        '失败',
        '删除设备分组失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 保存设备分组到存储
  Future<void> _saveDeviceGroups() async {
    try {
      final groupsJson = deviceGroups.map((group) => group.toJson()).toList();
      await _storageService.saveJson(StorageKeys.deviceGroups, groupsJson);
      LogService.to.i('保存了 ${deviceGroups.length} 个设备分组');
    } catch (e) {
      LogService.to.e('保存设备分组失败', e);
      rethrow;
    }
  }

  /// 切换设备收藏状态
  Future<void> toggleFavorite(String entityId) async {
    try {
      if (favoriteDevices.contains(entityId)) {
        // 取消收藏
        favoriteDevices.remove(entityId);
      } else {
        // 添加收藏
        favoriteDevices.add(entityId);
      }

      // 保存到存储
      await _saveFavoriteDevices();

      // 显示成功消息
      final entity = haService.getEntity(entityId);
      final name = entity?.friendlyName ?? entityId;
      final action = favoriteDevices.contains(entityId) ? '收藏' : '取消收藏';

      Get.snackbar(
        '成功',
        '已$action设备: $name',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('切换设备收藏状态失败', e);
      Get.snackbar(
        '失败',
        '操作失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 保存收藏设备到存储
  Future<void> _saveFavoriteDevices() async {
    try {
      await _storageService.saveList(StorageKeys.favoriteDevices, favoriteDevices);
      LogService.to.i('保存了 ${favoriteDevices.length} 个收藏设备');
    } catch (e) {
      LogService.to.e('保存收藏设备失败', e);
      rethrow;
    }
  }

  /// 设置设备别名
  Future<void> setDeviceAlias(String entityId, String alias) async {
    try {
      // 如果别名为空，则删除别名
      if (alias.isEmpty) {
        deviceAliases.remove(entityId);
      } else {
        deviceAliases[entityId] = alias;
      }

      // 保存到存储
      await _saveDeviceAliases();

      // 显示成功消息
      final entity = haService.getEntity(entityId);
      final name = entity?.friendlyName ?? entityId;

      Get.snackbar(
        '成功',
        '已设置设备别名: $name -> $alias',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LogService.to.e('设置设备别名失败', e);
      Get.snackbar(
        '失败',
        '设置设备别名失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 保存设备别名到存储
  Future<void> _saveDeviceAliases() async {
    try {
      await _storageService.saveMap(StorageKeys.deviceAliases, deviceAliases);
      LogService.to.i('保存了 ${deviceAliases.length} 个设备别名');
    } catch (e) {
      LogService.to.e('保存设备别名失败', e);
      rethrow;
    }
  }

  /// 获取设备别名
  String? getDeviceAlias(String entityId) {
    return deviceAliases[entityId];
  }

  /// 设置当前选中的分组
  void setSelectedGroup(String groupId) {
    selectedGroupId.value = groupId;
  }

  /// 切换收藏设备显示状态
  void toggleShowFavorites() {
    showFavorites.value = !showFavorites.value;
  }

  /// 重新创建TabController
  void recreateTabController() {
    try {
      LogService.to.i('开始重新创建TabController');

      // 保存当前选中的索引
      int currentIndex = 0;
      try {
        currentIndex = tabController.index;
      } catch (e) {
        LogService.to.d('获取当前索引失败，使用默认值0');
      }

      // 安全地释放旧控制器
      try {
        tabController.dispose();
      } catch (e) {
        LogService.to.d('释放旧TabController失败，可能已经被释放');
      }

      // 确保域名列表不为空
      if (domains.isEmpty && haService.entities.isNotEmpty) {
        LogService.to.w('域名列表为空但实体列表不为空，尝试重新生成域名列表');
        updateDomains();
      }

      // 创建新的控制器，确保长度与标签数量匹配
      if (domains.isNotEmpty) {
        // 记录域名列表，用于调试
        LogService.to.d('域名列表: $domains');

        // 创建新的TabController
        tabController = TabController(
          length: domains.length,
          vsync: this,
          initialIndex: currentIndex < domains.length ? currentIndex : 0,
        );
        LogService.to.i('TabController重新创建成功，长度: ${domains.length}');

        // 验证TabController是否正确创建
        if (tabController.length != domains.length) {
          LogService.to.e('TabController创建异常：长度(${tabController.length})与域名数量(${domains.length})不匹配');
        }
      } else {
        // 如果域名列表为空，创建一个默认长度为1的控制器
        LogService.to.w('警告：域名列表为空，创建默认TabController');

        // 确保至少有"all"域名
        domains.value = ['all'];

        tabController = TabController(
          length: 1,
          vsync: this,
        );
      }

      // 强制更新UI
      update();

      // 延迟检查TabController状态
      Future.delayed(const Duration(milliseconds: 100), () {
        if (tabController.length != domains.length) {
          LogService.to.w('延迟检查：TabController长度(${tabController.length})与域名数量(${domains.length})不匹配');
        } else {
          LogService.to.d('延迟检查：TabController状态正常');
        }
      });
    } catch (e, stackTrace) {
      LogService.to.e('重新创建TabController失败', e, stackTrace);

      // 尝试恢复
      try {
        LogService.to.i('尝试恢复TabController');

        // 确保域名列表至少有"all"
        if (domains.isEmpty) {
          domains.value = ['all'];
        }

        // 创建新的TabController
        tabController = TabController(
          length: domains.length,
          vsync: this,
        );

        // 强制更新UI
        update();

        LogService.to.i('TabController恢复成功');
      } catch (e2) {
        LogService.to.e('TabController恢复失败', e2);
      }
    }
  }
}
