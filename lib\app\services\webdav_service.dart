import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webdav_client/webdav_client.dart';
import '../data/local/storage_keys.dart';
import '../data/local/storage_service.dart';
import 'log_service.dart';

/// WebDAV服务，提供云备份和恢复功能
class WebDavService extends GetxService {
  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();

  // WebDAV客户端
  Client? _client;

  // WebDAV配置
  final RxString serverUrl = ''.obs;
  final RxString username = ''.obs;
  final RxString password = ''.obs;
  final RxString remotePath = '/ha_smart_home/'.obs;

  // 连接状态
  final RxBool isConnected = false.obs;
  final RxBool isConnecting = false.obs;
  final Rx<String?> connectionError = Rx<String?>(null);

  // 备份状态
  final RxBool isBackingUp = false.obs;
  final RxBool isRestoring = false.obs;
  final RxDouble backupProgress = 0.0.obs;
  final RxDouble restoreProgress = 0.0.obs;

  // 备份列表
  final RxList<WebDavBackupInfo> backups = <WebDavBackupInfo>[].obs;

  /// 初始化WebDAV服务
  Future<WebDavService> init() async {
    LogService.to.i('初始化WebDAV服务');

    // 加载WebDAV配置
    _loadConfig();

    return this;
  }

  /// 加载WebDAV配置
  void _loadConfig() {
    try {
      serverUrl.value = _storageService.getString(StorageKeys.webdavServerUrl) ?? '';
      username.value = _storageService.getString(StorageKeys.webdavUsername) ?? '';
      password.value = _storageService.getString(StorageKeys.webdavPassword) ?? '';
      remotePath.value = _storageService.getString(StorageKeys.webdavRemotePath) ?? '/ha_smart_home/';

      LogService.to.i('已加载WebDAV配置: ${serverUrl.value}');
    } catch (e) {
      LogService.to.e('加载WebDAV配置失败', e);
    }
  }

  /// 保存WebDAV配置
  Future<void> saveConfig({
    required String serverUrl,
    required String username,
    required String password,
    String remotePath = '/ha_smart_home/',
  }) async {
    try {
      // 更新配置
      this.serverUrl.value = serverUrl;
      this.username.value = username;
      this.password.value = password;
      this.remotePath.value = remotePath;

      // 保存到存储
      await _storageService.saveString(StorageKeys.webdavServerUrl, serverUrl);
      await _storageService.saveString(StorageKeys.webdavUsername, username);
      await _storageService.saveString(StorageKeys.webdavPassword, password);
      await _storageService.saveString(StorageKeys.webdavRemotePath, remotePath);

      LogService.to.i('已保存WebDAV配置: $serverUrl');

      // 重置连接状态
      isConnected.value = false;
      connectionError.value = null;
    } catch (e) {
      LogService.to.e('保存WebDAV配置失败', e);
      throw Exception('保存WebDAV配置失败: $e');
    }
  }

  /// 连接到WebDAV服务器
  Future<bool> connect() async {
    if (serverUrl.isEmpty || username.isEmpty || password.isEmpty) {
      connectionError.value = '请先配置WebDAV服务器信息';
      return false;
    }

    try {
      isConnecting.value = true;
      connectionError.value = null;

      LogService.to.i('正在连接WebDAV服务器: ${serverUrl.value}');

      // 创建WebDAV客户端
      _client = newClient(
        serverUrl.value,
        user: username.value,
        password: password.value,
        debug: true,
      );

      // 测试连接
      await _client!.ping();

      // 确保远程目录存在
      await _ensureRemoteDirectoryExists();

      isConnected.value = true;
      isConnecting.value = false;
      LogService.to.i('WebDAV连接成功');
      return true;
    } catch (e) {
      isConnected.value = false;
      isConnecting.value = false;
      connectionError.value = '连接WebDAV服务器失败: $e';
      LogService.to.e('连接WebDAV服务器失败', e);
      return false;
    }
  }

  /// 确保远程目录存在
  Future<void> _ensureRemoteDirectoryExists() async {
    try {
      if (_client == null) return;

      try {
        // 尝试读取目录，如果不存在会抛出异常
        await _client!.readDir(remotePath.value);
        LogService.to.i('远程目录已存在: ${remotePath.value}');
      } catch (e) {
        // 目录不存在，创建它
        await _client!.mkdir(remotePath.value);
        LogService.to.i('已创建远程目录: ${remotePath.value}');
      }
    } catch (e) {
      LogService.to.e('确保远程目录存在失败', e);
      throw Exception('确保远程目录存在失败: $e');
    }
  }

  /// 获取备份列表
  Future<List<WebDavBackupInfo>> getBackups() async {
    try {
      if (_client == null) {
        final connected = await connect();
        if (!connected) {
          throw Exception('未连接到WebDAV服务器');
        }
      }

      // 获取远程文件列表
      final files = await _client!.readDir(remotePath.value);

      // 过滤并转换为备份信息
      final backupFiles = files
          .where((file) => file.name?.endsWith('.json') == true && file.name?.startsWith('backup_') == true)
          .map((file) => WebDavBackupInfo(
                name: file.name ?? 'unknown.json',
                path: file.path ?? '',
                size: file.size ?? 0,
                date: file.mTime ?? DateTime.now(),
              ))
          .toList();

      // 按日期排序
      backupFiles.sort((a, b) => b.date.compareTo(a.date));

      // 更新备份列表
      backups.value = backupFiles;

      LogService.to.i('获取到 ${backupFiles.length} 个备份');
      return backupFiles;
    } catch (e) {
      LogService.to.e('获取备份列表失败', e);
      return [];
    }
  }

  /// 创建备份
  Future<bool> createBackup() async {
    try {
      if (_client == null) {
        final connected = await connect();
        if (!connected) {
          throw Exception('未连接到WebDAV服务器');
        }
      }

      isBackingUp.value = true;
      backupProgress.value = 0.0;

      // 获取应用数据
      final backupData = await _collectBackupData();
      backupProgress.value = 0.3;

      // 创建备份文件名
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').replaceAll('.', '-');
      final backupFileName = 'backup_$timestamp.json';
      final remotePath = '${this.remotePath.value}$backupFileName';

      // 将数据转换为JSON
      final jsonData = jsonEncode(backupData);
      backupProgress.value = 0.6;

      // 上传到WebDAV服务器
      await _client!.write(
        remotePath,
        Uint8List.fromList(utf8.encode(jsonData)),
      );
      backupProgress.value = 1.0;

      // 刷新备份列表
      await getBackups();

      isBackingUp.value = false;
      LogService.to.i('备份创建成功: $backupFileName');
      return true;
    } catch (e) {
      isBackingUp.value = false;
      backupProgress.value = 0.0;
      LogService.to.e('创建备份失败', e);
      return false;
    }
  }

  /// 收集备份数据
  Future<Map<String, dynamic>> _collectBackupData() async {
    try {
      // 获取所有需要备份的数据
      final backupData = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'version': 1,
        'data': {
          // 房间数据
          'rooms': _storageService.getList<Map<String, dynamic>>('home_rooms') ?? [],

          // 卡片数据
          'cards': _storageService.getList<Map<String, dynamic>>('home_cards') ?? [],

          // 场景数据
          'scenes': _storageService.getList<Map<String, dynamic>>(StorageKeys.scenes) ?? [],

          // 场景分组数据
          'scene_groups': _storageService.getList<Map<String, dynamic>>(StorageKeys.sceneGroups) ?? [],

          // 设备分组数据
          'device_groups': _storageService.getJson(StorageKeys.deviceGroups) ?? [],

          // 设备别名数据
          'device_aliases': _storageService.getJson(StorageKeys.deviceAliases) ?? {},

          // 收藏的设备
          'favorite_devices': _storageService.getList<String>(StorageKeys.favoriteDevices) ?? [],

          // 收藏的场景
          'favorite_scenes': _storageService.getList<String>(StorageKeys.favoriteScenes) ?? [],

          // 设置数据
          'settings': {
            'theme_mode': _storageService.getString(StorageKeys.themeMode) ?? 'system',
            'weather_api_key': _storageService.getString(StorageKeys.weatherApiKey) ?? '',
            'weather_location': _storageService.getString(StorageKeys.weatherLocation) ?? '',
          },
        },
      };

      return backupData;
    } catch (e) {
      LogService.to.e('收集备份数据失败', e);
      throw Exception('收集备份数据失败: $e');
    }
  }

  /// 恢复备份
  Future<bool> restoreBackup(WebDavBackupInfo backup) async {
    try {
      if (_client == null) {
        final connected = await connect();
        if (!connected) {
          throw Exception('未连接到WebDAV服务器');
        }
      }

      isRestoring.value = true;
      restoreProgress.value = 0.0;

      // 下载备份文件
      final data = await _client!.read(backup.path);
      restoreProgress.value = 0.3;

      // 解析JSON数据
      final jsonData = utf8.decode(data);
      final backupData = jsonDecode(jsonData) as Map<String, dynamic>;
      restoreProgress.value = 0.6;

      // 恢复数据
      await _restoreBackupData(backupData);
      restoreProgress.value = 1.0;

      isRestoring.value = false;
      LogService.to.i('备份恢复成功: ${backup.name}');
      return true;
    } catch (e) {
      isRestoring.value = false;
      restoreProgress.value = 0.0;
      LogService.to.e('恢复备份失败', e);
      return false;
    }
  }

  /// 恢复备份数据
  Future<void> _restoreBackupData(Map<String, dynamic> backupData) async {
    try {
      final data = backupData['data'] as Map<String, dynamic>;

      // 恢复房间数据
      if (data.containsKey('rooms')) {
        await _storageService.saveList('home_rooms', data['rooms'] as List);
      }

      // 恢复卡片数据
      if (data.containsKey('cards')) {
        await _storageService.saveList('home_cards', data['cards'] as List);
      }

      // 恢复场景数据
      if (data.containsKey('scenes')) {
        await _storageService.saveList(StorageKeys.scenes, data['scenes'] as List);
      }

      // 恢复场景分组数据
      if (data.containsKey('scene_groups')) {
        await _storageService.saveList(StorageKeys.sceneGroups, data['scene_groups'] as List);
      }

      // 恢复设备分组数据
      if (data.containsKey('device_groups')) {
        await _storageService.saveJson(StorageKeys.deviceGroups, data['device_groups']);
      }

      // 恢复设备别名数据
      if (data.containsKey('device_aliases')) {
        await _storageService.saveJson(StorageKeys.deviceAliases, data['device_aliases']);
      }

      // 恢复收藏的设备
      if (data.containsKey('favorite_devices')) {
        await _storageService.saveList(StorageKeys.favoriteDevices, data['favorite_devices'] as List);
      }

      // 恢复收藏的场景
      if (data.containsKey('favorite_scenes')) {
        await _storageService.saveList(StorageKeys.favoriteScenes, data['favorite_scenes'] as List);
      }

      // 恢复设置数据
      if (data.containsKey('settings')) {
        final settings = data['settings'] as Map<String, dynamic>;

        if (settings.containsKey('theme_mode')) {
          await _storageService.saveString(StorageKeys.themeMode, settings['theme_mode'] as String);
        }

        if (settings.containsKey('weather_api_key')) {
          await _storageService.saveString(StorageKeys.weatherApiKey, settings['weather_api_key'] as String);
        }

        if (settings.containsKey('weather_location')) {
          await _storageService.saveString(StorageKeys.weatherLocation, settings['weather_location'] as String);
        }
      }
    } catch (e) {
      LogService.to.e('恢复备份数据失败', e);
      throw Exception('恢复备份数据失败: $e');
    }
  }

  /// 删除备份
  Future<bool> deleteBackup(WebDavBackupInfo backup) async {
    try {
      if (_client == null) {
        final connected = await connect();
        if (!connected) {
          throw Exception('未连接到WebDAV服务器');
        }
      }

      // 删除备份文件
      await _client!.remove(backup.path);

      // 刷新备份列表
      await getBackups();

      LogService.to.i('备份删除成功: ${backup.name}');
      return true;
    } catch (e) {
      LogService.to.e('删除备份失败', e);
      return false;
    }
  }
}

/// WebDAV备份信息
class WebDavBackupInfo {
  final String name;
  final String path;
  final int size;
  final DateTime date;

  WebDavBackupInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.date,
  });

  /// 获取格式化的大小
  String get formattedSize {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }
}
