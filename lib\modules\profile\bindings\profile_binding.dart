import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../controllers/statistics_controller.dart';
import '../controllers/settings_controller.dart';
import '../controllers/backup_controller.dart';
import '../../../app/services/statistics_service.dart';
import '../../../app/services/webdav_service.dart';

/// 设置模块绑定
class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    // 注册服务
    Get.putAsync<StatisticsService>(() => StatisticsService().init());
    Get.putAsync<WebDavService>(() => WebDavService().init());

    // 注册控制器
    Get.lazyPut<ProfileController>(() => ProfileController());
    Get.lazyPut<StatisticsController>(() => StatisticsController());
    Get.lazyPut<SettingsController>(() => SettingsController());
    Get.lazyPut<BackupController>(() => BackupController());
  }
}
