import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../shared/widgets/error_view.dart';
import '../../../shared/widgets/loading_indicator.dart';
import '../controllers/device_detail_controller.dart';
import 'widgets/attribute_item.dart';
import 'widgets/control_panel.dart';

/// 设备详情视图
class DeviceDetailView extends GetView<DeviceDetailController> {
  const DeviceDetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.entity.value?.friendlyName ?? '设备详情')),
        actions: [
          Obx(() {
            if (controller.entity.value?.supportsTurnOn == true) {
              return IconButton(
                icon: Icon(
                  controller.entity.value?.isOn == true
                      ? Icons.toggle_on
                      : Icons.toggle_off,
                ),
                onPressed: controller.toggleEntity,
                tooltip: controller.entity.value?.isOn == true ? '关闭' : '开启',
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: '加载设备信息...');
        }
        
        if (controller.errorMessage.value != null) {
          return ErrorView(
            message: controller.errorMessage.value!,
            onRetry: controller.loadEntity,
          );
        }
        
        final entity = controller.entity.value;
        if (entity == null) {
          return const ErrorView(
            message: '设备不存在或无法访问',
            icon: Icons.device_unknown,
          );
        }
        
        return _buildDeviceDetail(context, entity);
      }),
    );
  }
  
  /// 构建设备详情
  Widget _buildDeviceDetail(BuildContext context, EntityModel entity) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 设备状态卡片
          _buildStatusCard(context, entity),
          const SizedBox(height: 16.0),
          
          // 控制面板（如果支持）
          if (_supportsControl(entity)) ...[
            _buildControlPanel(context, entity),
            const SizedBox(height: 16.0),
          ],
          
          // 设备属性
          _buildAttributesCard(context, entity),
        ],
      ),
    );
  }
  
  /// 构建状态卡片
  Widget _buildStatusCard(BuildContext context, EntityModel entity) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getEntityIcon(entity),
                  size: 36.0,
                  color: _getStateColor(context, entity),
                ),
                const SizedBox(width: 16.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        entity.friendlyName,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        entity.entityId,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Divider(height: 32.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '当前状态',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Chip(
                  label: Text(_getStateText(entity)),
                  backgroundColor: _getStateColor(context, entity).withOpacity(0.2),
                  labelStyle: TextStyle(
                    color: _getStateColor(context, entity),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Text(
              '最后更新: ${_formatDateTime(entity.lastUpdated)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建控制面板
  Widget _buildControlPanel(BuildContext context, EntityModel entity) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '控制面板',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16.0),
            ControlPanel(
              entity: entity,
              controller: controller,
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建属性卡片
  Widget _buildAttributesCard(BuildContext context, EntityModel entity) {
    final attributes = entity.attributes.entries.toList();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '设备属性',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16.0),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: attributes.length,
              itemBuilder: (context, index) {
                final attribute = attributes[index];
                return AttributeItem(
                  name: attribute.key,
                  value: attribute.value.toString(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 检查实体是否支持控制
  bool _supportsControl(EntityModel entity) {
    return entity.supportsTurnOn ||
           entity.supportsBrightness ||
           entity.supportsColor ||
           entity.supportsColorTemp ||
           entity.supportsFanSpeed ||
           entity.supportsTemperature ||
           entity.supportsHumidity ||
           entity.supportsLock ||
           entity.supportsCover ||
           entity.supportsMedia;
  }
  
  /// 获取实体图标
  IconData _getEntityIcon(EntityModel entity) {
    // 根据实体类型返回图标
    switch (entity.domain) {
      case 'light':
        return entity.isOn ? Icons.lightbulb : Icons.lightbulb_outline;
      case 'switch':
        return entity.isOn ? Icons.toggle_on : Icons.toggle_off;
      case 'fan':
        return Icons.air;
      case 'climate':
        return Icons.thermostat;
      case 'sensor':
        if (entity.deviceClass == 'temperature') {
          return Icons.thermostat;
        } else if (entity.deviceClass == 'humidity') {
          return Icons.water_drop;
        } else {
          return Icons.sensors;
        }
      case 'binary_sensor':
        if (entity.deviceClass == 'door') {
          return entity.isOn ? Icons.door_front_door : Icons.door_front_door;
        } else if (entity.deviceClass == 'window') {
          return entity.isOn ? Icons.blinds : Icons.blinds_closed;
        } else if (entity.deviceClass == 'motion') {
          return Icons.motion_photos_on;
        } else {
          return Icons.sensors;
        }
      case 'cover':
        return entity.isOn ? Icons.blinds : Icons.blinds_closed;
      case 'lock':
        return entity.isOn ? Icons.lock_open : Icons.lock;
      case 'media_player':
        return Icons.tv;
      default:
        return Icons.devices;
    }
  }
  
  /// 获取状态文本
  String _getStateText(EntityModel entity) {
    if (entity.isUnavailable) {
      return '不可用';
    }
    
    if (entity.isUnknown) {
      return '未知';
    }
    
    // 对于传感器，显示状态值和单位
    if (entity.isSensor) {
      if (entity.unitOfMeasurement != null) {
        return '${entity.state} ${entity.unitOfMeasurement}';
      }
      return entity.state;
    }
    
    // 对于二进制传感器，根据设备类型显示状态
    if (entity.isBinarySensor) {
      switch (entity.deviceClass) {
        case 'door':
          return entity.isOn ? '开启' : '关闭';
        case 'window':
          return entity.isOn ? '开启' : '关闭';
        case 'motion':
          return entity.isOn ? '有动作' : '无动作';
        case 'presence':
          return entity.isOn ? '在家' : '离家';
        default:
          return entity.isOn ? '开启' : '关闭';
      }
    }
    
    // 对于开关和灯，显示开/关状态
    if (entity.isSwitch || entity.isLight) {
      return entity.isOn ? '开启' : '关闭';
    }
    
    // 对于风扇，显示开/关状态
    if (entity.isFan) {
      return entity.isOn ? '开启' : '关闭';
    }
    
    // 对于锁，显示锁定/解锁状态
    if (entity.isLock) {
      return entity.isOn ? '解锁' : '锁定';
    }
    
    // 对于窗帘，显示开/关状态
    if (entity.isCover) {
      return entity.isOn ? '开启' : '关闭';
    }
    
    return entity.state;
  }
  
  /// 获取状态颜色
  Color _getStateColor(BuildContext context, EntityModel entity) {
    if (entity.isUnavailable) {
      return Theme.of(context).colorScheme.error;
    }
    
    if (entity.isUnknown) {
      return Theme.of(context).colorScheme.onSurfaceVariant;
    }
    
    if (entity.isOn) {
      return Theme.of(context).colorScheme.primary;
    }
    
    return Theme.of(context).colorScheme.onSurfaceVariant;
  }
  
  /// 格式化日期时间
  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inSeconds < 60) {
        return '${difference.inSeconds}秒前';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else {
        return '${difference.inDays}天前';
      }
    } catch (e) {
      return dateTimeString;
    }
  }
}
