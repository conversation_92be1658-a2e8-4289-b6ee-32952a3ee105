import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/services/log_service.dart';
import '../../../shared/widgets/custom_bottom_nav.dart';
import '../../../app/config/app_routes.dart';
import '../controllers/home_controller.dart';
import '../controllers/card_editor_controller.dart';
import '../widgets/cards/base_card.dart';
import '../widgets/cards/weather_card.dart';
import '../widgets/cards/sensor_card.dart';
import '../widgets/cards/light_card.dart';
import '../widgets/resizable_card_wrapper.dart';
import 'components/room_selector.dart';

/// 首页视图
class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    // 在页面构建完成后，确保登录页面已完全移除
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 确保页面已完全加载
      if (!controller.isPageLoaded.value) {
        controller.isPageLoaded.value = true;
        LogService.to.i('首页视图已完全加载');
      }
    });

    // 获取卡片编辑控制器
    final cardEditorController = Get.find<CardEditorController>();

    // 使用Container包裹Scaffold，确保背景颜色正确
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor, // 确保背景颜色正确
        appBar: AppBar(
          title: const Text('智能家居控制中心'),
          actions: [
            // 编辑模式按钮
            Obx(() => IconButton(
              icon: Icon(
                controller.isEditMode.value ? Icons.done : Icons.edit,
                color: controller.isEditMode.value ? Colors.green : null,
              ),
              onPressed: controller.toggleEditMode,
              tooltip: controller.isEditMode.value ? '完成编辑' : '编辑模式',
            )),

            // 更多菜单
            PopupMenuButton(
              icon: const Icon(Icons.more_vert),
              itemBuilder: (context) => [
                // 主题切换
                PopupMenuItem(
                  child: Row(
                    children: [
                      Icon(
                        Get.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                        size: 20,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      const SizedBox(width: 8),
                      Text(Get.isDarkMode ? '切换到亮色模式' : '切换到暗色模式'),
                    ],
                  ),
                  onTap: () {
                    Get.changeThemeMode(
                      Get.isDarkMode ? ThemeMode.light : ThemeMode.dark,
                    );
                  },
                ),

                // 设置选项
                PopupMenuItem(
                  child: Row(
                    children: [
                      Icon(
                        Icons.settings,
                        size: 20,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      const SizedBox(width: 8),
                      const Text('设置'),
                    ],
                  ),
                  onTap: () => Get.toNamed(AppRoutes.settings),
                ),
              ],
            ),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            // 刷新数据
            Get.find<CardEditorController>().loadCards();

            // 显示提示
            Get.snackbar(
              '刷新成功',
              '已更新首页数据',
              snackPosition: SnackPosition.BOTTOM,
            );
          },
          child: CustomScrollView(
            slivers: [
              // 房间选择器
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
                  child: RoomSelector(),
                ),
              ),

              // 卡片网格
              SliverPadding(
                padding: const EdgeInsets.all(8.0),
                sliver: Obx(() {
                  // 获取当前选中房间的ID
                  final selectedRoomId = controller.selectedRoom.value?.id;

                  // 过滤卡片
                  final filteredCards = selectedRoomId == null
                      // 如果是"全部房间"，显示所有卡片
                      ? cardEditorController.cards
                      // 否则只显示属于当前选中房间的卡片
                      : cardEditorController.cards
                          .where((card) => card.roomId == selectedRoomId)
                          .toList();

                  // 如果没有卡片，显示提示
                  if (filteredCards.isEmpty) {
                    return SliverToBoxAdapter(
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.dashboard_customize,
                                size: 64,
                                color: Theme.of(context).colorScheme.primary.withAlpha(128),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                '当前房间没有卡片',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '点击右下角的按钮添加卡片',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }

                  // 显示卡片网格
                  return SliverGrid(
                    gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                      maxCrossAxisExtent: 600.0,
                      mainAxisSpacing: 8.0,
                      crossAxisSpacing: 8.0,
                      childAspectRatio: 2.0,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final card = filteredCards[index];
                        return _buildCard(context, card);
                      },
                      childCount: filteredCards.length,
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
        // 编辑模式下显示悬浮按钮
        floatingActionButton: Obx(() => controller.isEditMode.value
            ? FloatingActionButton(
                onPressed: () => Get.toNamed('/home/<USER>'),
                tooltip: '编辑卡片',
                child: const Icon(Icons.dashboard_customize),
              )
            : const SizedBox.shrink()),
        // 在主容器中使用，不需要底部导航栏
        // bottomNavigationBar: CustomBottomNav(
        //   currentIndex: 0,
        // ),
      ),
    );
  }

  /// 构建卡片
  Widget _buildCard(BuildContext context, CardModel card) {
    // 根据卡片类型构建不同的卡片
    Widget cardWidget;

    switch (card.type) {
      case CardType.weather:
        cardWidget = WeatherCard(
          id: card.id,
          style: card.style,
          size: card.size,
          editMode: controller.isEditMode.value,
          customTitle: card.customTitle,
          showTitle: card.showTitle,
          onDelete: controller.isEditMode.value ? () => _showDeleteConfirmDialog(context, card) : null,
          onEdit: controller.isEditMode.value ? () => Get.toNamed('/home/<USER>') : null,
          entityId: card.entityId ?? 'weather.forecast_wo_de_jia',
          forecastType: card.forecastType,
        );
        break;

      case CardType.light:
        if (card.entityId != null) {
          cardWidget = LightCard(
            id: card.id,
            entityId: card.entityId!,
            style: card.style,
            size: card.size,
            editMode: controller.isEditMode.value,
            customTitle: card.customTitle,
            showTitle: card.showTitle,
            onDelete: controller.isEditMode.value ? () => _showDeleteConfirmDialog(context, card) : null,
            onEdit: controller.isEditMode.value ? () => Get.toNamed('/home/<USER>') : null,
          );
        } else {
          cardWidget = _buildErrorCard(context, card);
        }
        break;

      case CardType.sensor:
        if (card.entityId != null) {
          cardWidget = SensorCard(
            id: card.id,
            entityId: card.entityId!,
            style: card.style,
            size: card.size,
            editMode: controller.isEditMode.value,
            customTitle: card.customTitle,
            showTitle: card.showTitle,
            onDelete: controller.isEditMode.value ? () => _showDeleteConfirmDialog(context, card) : null,
            onEdit: controller.isEditMode.value ? () => Get.toNamed('/home/<USER>') : null,
          );
        } else {
          cardWidget = _buildErrorCard(context, card);
        }
        break;

      default:
        // 默认卡片
        cardWidget = BaseCard(
          id: card.id,
          title: _getCardTypeName(card.type),
          customTitle: card.customTitle,
          showTitle: card.showTitle,
          icon: _getCardTypeIcon(card.type),
          style: card.style,
          size: card.size,
          editMode: controller.isEditMode.value,
          onDelete: controller.isEditMode.value ? () => _showDeleteConfirmDialog(context, card) : null,
          onEdit: controller.isEditMode.value ? () => Get.toNamed('/home/<USER>') : null,
          content: const Center(
            child: Text('此卡片类型正在开发中...'),
          ),
        );
    }

    // 使用可调整大小的卡片包装组件
    return ResizableCardWrapper(
      cardId: card.id,
      currentSize: card.size,
      editMode: controller.isEditMode.value,
      child: cardWidget,
    );
  }

  /// 构建错误卡片
  Widget _buildErrorCard(BuildContext context, CardModel card) {
    return BaseCard(
      id: card.id,
      title: '错误',
      customTitle: card.customTitle,
      showTitle: card.showTitle,
      icon: Icons.error,
      style: card.style,
      size: card.size,
      editMode: controller.isEditMode.value,
      onDelete: controller.isEditMode.value ? () => _showDeleteConfirmDialog(context, card) : null,
      onEdit: controller.isEditMode.value ? () => Get.toNamed('/home/<USER>') : null,
      content: const Center(
        child: Text('卡片配置错误'),
      ),
    );
  }



  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, CardModel card) {
    Get.dialog(
      AlertDialog(
        title: const Text('删除卡片'),
        content: Text('确定要删除"${_getCardTypeName(card.type)}"卡片吗？'),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),

          // 删除按钮
          TextButton(
            onPressed: () {
              Get.find<CardEditorController>().deleteCard(card.id);
              Get.back();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 获取卡片类型图标
  IconData _getCardTypeIcon(CardType type) {
    switch (type) {
      case CardType.weather:
        return Icons.cloud;
      case CardType.light:
        return Icons.lightbulb;
      case CardType.climate:
        return Icons.thermostat;
      case CardType.camera:
        return Icons.videocam;
      case CardType.sensor:
        return Icons.sensors;
      case CardType.cover:
        return Icons.blinds;
      case CardType.lock:
        return Icons.lock;
      case CardType.media:
        return Icons.music_note;
      case CardType.purifier:
        return Icons.air;
      case CardType.vacuum:
        return Icons.cleaning_services;
      case CardType.energy:
        return Icons.bolt;
    }
  }

  /// 获取卡片类型名称
  String _getCardTypeName(CardType type) {
    switch (type) {
      case CardType.weather:
        return '天气';
      case CardType.light:
        return '灯光';
      case CardType.climate:
        return '温控';
      case CardType.camera:
        return '摄像头';
      case CardType.sensor:
        return '传感器';
      case CardType.cover:
        return '窗帘';
      case CardType.lock:
        return '门锁';
      case CardType.media:
        return '媒体';
      case CardType.purifier:
        return '净化器';
      case CardType.vacuum:
        return '扫地机';
      case CardType.energy:
        return '能源';
    }
  }
}
