import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../local/storage_keys.dart';
import '../local/storage_service.dart';
import '../models/scene_model.dart';
import '../models/scene_group_model.dart';
import '../models/entity_model.dart';
import '../../services/ha_service.dart';
import '../../services/log_service.dart';

/// 场景数据仓库
class SceneRepository {
  // 存储服务
  final StorageService _storageService = Get.find<StorageService>();
  
  // Home Assistant服务
  final HaService _haService = Get.find<HaService>();
  
  /// 获取所有场景
  Future<List<SceneModel>> getAllScenes() async {
    try {
      // 从存储中获取自定义场景
      final customScenes = await _getCustomScenes();
      
      // 从Home Assistant获取内置场景
      final haScenes = await _getHaScenes();
      
      // 合并场景列表
      final allScenes = [...customScenes, ...haScenes];
      
      // 按顺序排序
      allScenes.sort((a, b) => a.order.compareTo(b.order));
      
      return allScenes;
    } catch (e) {
      LogService.to.e('获取场景失败', e);
      return [];
    }
  }
  
  /// 获取自定义场景
  Future<List<SceneModel>> _getCustomScenes() async {
    try {
      final scenes = _storageService.getList<Map<String, dynamic>>(StorageKeys.scenes);
      
      if (scenes == null || scenes.isEmpty) {
        return [];
      }
      
      return scenes.map((json) => SceneModel.fromJson(json)).toList();
    } catch (e) {
      LogService.to.e('获取自定义场景失败', e);
      return [];
    }
  }
  
  /// 获取Home Assistant内置场景
  Future<List<SceneModel>> _getHaScenes() async {
    try {
      // 获取所有场景实体
      final sceneEntities = _haService.getEntitiesByDomain('scene');
      
      // 转换为场景模型
      return sceneEntities.map((entity) => _convertEntityToScene(entity)).toList();
    } catch (e) {
      LogService.to.e('获取Home Assistant场景失败', e);
      return [];
    }
  }
  
  /// 将实体转换为场景模型
  SceneModel _convertEntityToScene(EntityModel entity) {
    // 生成随机颜色
    final color = Colors.primaries[entity.entityId.hashCode % Colors.primaries.length];
    
    return SceneModel(
      id: entity.entityId,
      name: entity.friendlyName,
      icon: entity.icon,
      color: color,
      entityIds: [entity.entityId],
      isCustom: false,
    );
  }
  
  /// 保存场景
  Future<bool> saveScene(SceneModel scene) async {
    try {
      // 获取现有场景
      final scenes = await _getCustomScenes();
      
      // 检查是否已存在
      final index = scenes.indexWhere((s) => s.id == scene.id);
      
      if (index >= 0) {
        // 更新现有场景
        scenes[index] = scene;
      } else {
        // 添加新场景
        scenes.add(scene);
      }
      
      // 保存到存储
      await _saveScenes(scenes);
      
      return true;
    } catch (e) {
      LogService.to.e('保存场景失败', e);
      return false;
    }
  }
  
  /// 删除场景
  Future<bool> deleteScene(String sceneId) async {
    try {
      // 获取现有场景
      final scenes = await _getCustomScenes();
      
      // 移除指定场景
      scenes.removeWhere((scene) => scene.id == sceneId);
      
      // 保存到存储
      await _saveScenes(scenes);
      
      return true;
    } catch (e) {
      LogService.to.e('删除场景失败', e);
      return false;
    }
  }
  
  /// 保存场景列表
  Future<void> _saveScenes(List<SceneModel> scenes) async {
    final jsonList = scenes.map((scene) => scene.toJson()).toList();
    await _storageService.saveList(StorageKeys.scenes, jsonList);
  }
  
  /// 获取所有场景分组
  Future<List<SceneGroupModel>> getAllSceneGroups() async {
    try {
      final groups = _storageService.getList<Map<String, dynamic>>(StorageKeys.sceneGroups);
      
      if (groups == null || groups.isEmpty) {
        return [];
      }
      
      final result = groups.map((json) => SceneGroupModel.fromJson(json)).toList();
      
      // 按顺序排序
      result.sort((a, b) => a.order.compareTo(b.order));
      
      return result;
    } catch (e) {
      LogService.to.e('获取场景分组失败', e);
      return [];
    }
  }
  
  /// 保存场景分组
  Future<bool> saveSceneGroup(SceneGroupModel group) async {
    try {
      // 获取现有分组
      final groups = await getAllSceneGroups();
      
      // 检查是否已存在
      final index = groups.indexWhere((g) => g.id == group.id);
      
      if (index >= 0) {
        // 更新现有分组
        groups[index] = group;
      } else {
        // 添加新分组
        groups.add(group);
      }
      
      // 保存到存储
      await _saveSceneGroups(groups);
      
      return true;
    } catch (e) {
      LogService.to.e('保存场景分组失败', e);
      return false;
    }
  }
  
  /// 删除场景分组
  Future<bool> deleteSceneGroup(String groupId) async {
    try {
      // 获取现有分组
      final groups = await getAllSceneGroups();
      
      // 移除指定分组
      groups.removeWhere((group) => group.id == groupId);
      
      // 保存到存储
      await _saveSceneGroups(groups);
      
      // 更新场景的分组ID
      final scenes = await _getCustomScenes();
      for (var scene in scenes) {
        if (scene.groupId == groupId) {
          await saveScene(scene.copyWith(groupId: null));
        }
      }
      
      return true;
    } catch (e) {
      LogService.to.e('删除场景分组失败', e);
      return false;
    }
  }
  
  /// 保存场景分组列表
  Future<void> _saveSceneGroups(List<SceneGroupModel> groups) async {
    final jsonList = groups.map((group) => group.toJson()).toList();
    await _storageService.saveList(StorageKeys.sceneGroups, jsonList);
  }
  
  /// 触发场景
  Future<bool> activateScene(SceneModel scene) async {
    try {
      if (scene.isCustom) {
        // 自定义场景：依次触发所有实体
        for (final entityId in scene.entityIds) {
          await _haService.turnOn(entityId);
        }
      } else {
        // Home Assistant场景：直接触发
        await _haService.turnOn(scene.id);
      }
      
      return true;
    } catch (e) {
      LogService.to.e('触发场景失败', e);
      return false;
    }
  }
}
