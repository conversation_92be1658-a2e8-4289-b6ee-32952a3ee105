import 'package:flutter/material.dart';

/// 骨架屏加载组件
class SkeletonLoader extends StatefulWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration duration;

  const SkeletonLoader({
    Key? key,
    this.width = double.infinity,
    this.height = 20,
    this.borderRadius = 8,
    this.baseColor,
    this.highlightColor,
    this.duration = const Duration(milliseconds: 1500),
  }) : super(key: key);

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseColor = widget.baseColor ?? theme.colorScheme.surfaceVariant.withAlpha(77); // 0.3 * 255 ≈ 77
    final highlightColor = widget.highlightColor ?? theme.colorScheme.surfaceVariant;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [baseColor, highlightColor, baseColor],
              stops: [
                _animation.value <= 0.0 ? 0.0 : _animation.value - 1.0,
                _animation.value,
                _animation.value >= 1.0 ? 1.0 : _animation.value,
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 设备列表骨架屏
class DeviceListSkeleton extends StatelessWidget {
  final int itemCount;

  const DeviceListSkeleton({
    Key? key,
    this.itemCount = 10,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                // 图标骨架
                SkeletonLoader(
                  width: 48,
                  height: 48,
                  borderRadius: 12,
                ),
                const SizedBox(width: 16.0),

                // 文本骨架
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonLoader(
                        width: double.infinity,
                        height: 20,
                      ),
                      const SizedBox(height: 8.0),
                      SkeletonLoader(
                        width: 100,
                        height: 16,
                      ),
                    ],
                  ),
                ),

                // 开关骨架
                SkeletonLoader(
                  width: 40,
                  height: 20,
                  borderRadius: 10,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
