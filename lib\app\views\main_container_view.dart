import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../config/app_routes.dart';
import '../../modules/home/<USER>/home_view.dart';
import '../../modules/scenes/views/scenes_view.dart';
import '../../modules/devices/views/devices_view.dart';
import '../../modules/profile/views/profile_view.dart';
import '../../shared/widgets/custom_bottom_nav.dart';

/// 主页面容器控制器
class MainContainerController extends GetxController {
  // 当前页面索引
  final RxInt currentIndex = 0.obs;

  // 切换页面
  void changePage(int index) {
    if (index != currentIndex.value) {
      currentIndex.value = index;
    }
  }

  // 获取当前路由名称
  String getCurrentRouteName() {
    switch (currentIndex.value) {
      case 0:
        return AppRoutes.home;
      case 1:
        return AppRoutes.scenes;
      case 2:
        return AppRoutes.devices;
      case 3:
        return AppRoutes.profile;
      default:
        return AppRoutes.home;
    }
  }
}

/// 主页面容器视图
/// 使用IndexedStack保持所有主要页面的状态
class MainContainerView extends StatelessWidget {
  const MainContainerView({super.key});

  @override
  Widget build(BuildContext context) {
    // 注册控制器
    final controller = Get.put(MainContainerController());

    return Scaffold(
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: const [
              // 首页
              HomeView(),
              // 场景页
              ScenesView(),
              // 设备页
              DevicesView(),
              // 设置页
              ProfileView(),
            ],
          )),
      bottomNavigationBar: Obx(() => CustomBottomNav(
            currentIndex: controller.currentIndex.value,
            onTap: (index) {
              controller.changePage(index);
            },
          )),
    );
  }
}
