/// 天气模型
class WeatherModel {
  /// 实体ID
  final String entityId;

  /// 状态（天气状况）
  final String state;

  /// 温度
  final double temperature;

  /// 湿度
  final int humidity;

  /// 露点
  final double? dewPoint;

  /// 温度单位
  final String temperatureUnit;

  /// 云覆盖率
  final int? cloudCoverage;

  /// 紫外线指数
  final double? uvIndex;

  /// 气压
  final double pressure;

  /// 气压单位
  final String pressureUnit;

  /// 风向
  final double? windBearing;

  /// 风速
  final double windSpeed;

  /// 风速单位
  final String windSpeedUnit;

  /// 能见度单位
  final String? visibilityUnit;

  /// 能见度（兼容旧代码）
  final double? visibility;

  /// 降水单位
  final String? precipitationUnit;

  /// 友好名称
  final String friendlyName;

  /// 最后更新时间
  final DateTime lastUpdated;

  /// 天气状况（兼容旧代码）
  String get condition => state;

  WeatherModel({
    required this.entityId,
    required this.state,
    required this.temperature,
    required this.humidity,
    this.dewPoint,
    required this.temperatureUnit,
    this.cloudCoverage,
    this.uvIndex,
    required this.pressure,
    required this.pressureUnit,
    this.windBearing,
    required this.windSpeed,
    required this.windSpeedUnit,
    this.visibilityUnit,
    this.visibility,
    this.precipitationUnit,
    required this.friendlyName,
    required this.lastUpdated,
  });

  /// 从Home Assistant API响应创建
  factory WeatherModel.fromHaResponse(Map<String, dynamic> json) {
    final attributes = json['attributes'] as Map<String, dynamic>;

    // 处理云覆盖率类型，可能是int或double
    int? cloudCoverageValue;
    if (attributes['cloud_coverage'] != null) {
      if (attributes['cloud_coverage'] is int) {
        cloudCoverageValue = attributes['cloud_coverage'] as int;
      } else if (attributes['cloud_coverage'] is double) {
        cloudCoverageValue = (attributes['cloud_coverage'] as double).round();
      }
    }

    return WeatherModel(
      entityId: json['entity_id'] as String,
      state: json['state'] as String,
      temperature: (attributes['temperature'] as num).toDouble(),
      humidity: attributes['humidity'] is int 
          ? attributes['humidity'] as int 
          : (attributes['humidity'] as num).round(),
      dewPoint: attributes['dew_point'] != null ? (attributes['dew_point'] as num).toDouble() : null,
      temperatureUnit: attributes['temperature_unit'] as String,
      cloudCoverage: cloudCoverageValue,
      uvIndex: attributes['uv_index'] != null ? (attributes['uv_index'] as num).toDouble() : null,
      pressure: (attributes['pressure'] as num).toDouble(),
      pressureUnit: attributes['pressure_unit'] as String,
      windBearing: attributes['wind_bearing'] != null ? (attributes['wind_bearing'] as num).toDouble() : null,
      windSpeed: (attributes['wind_speed'] as num).toDouble(),
      windSpeedUnit: attributes['wind_speed_unit'] as String,
      visibilityUnit: attributes['visibility_unit'] as String?,
      visibility: 10.0, // 默认值，兼容旧代码
      precipitationUnit: attributes['precipitation_unit'] as String?,
      friendlyName: attributes['friendly_name'] as String,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  /// 创建模拟天气数据（用于测试）
  factory WeatherModel.mock() {
    return WeatherModel(
      entityId: 'weather.forecast_home',
      state: 'sunny',
      temperature: 23.5,
      humidity: 65,
      temperatureUnit: '°C',
      pressure: 1013.5,
      pressureUnit: 'hPa',
      windSpeed: 3.2,
      windSpeedUnit: 'km/h',
      visibility: 10.0,
      friendlyName: '天气预报',
      lastUpdated: DateTime.now(),
    );
  }
}
