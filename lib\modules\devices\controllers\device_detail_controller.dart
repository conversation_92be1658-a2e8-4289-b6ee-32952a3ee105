import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../app/services/ha_service.dart';

/// 设备详情控制器
class DeviceDetailController extends GetxController {
  // 服务
  final HaService _haService = Get.find<HaService>();
  
  // 实体ID
  final String entityId;
  
  // 实体
  final Rx<EntityModel?> entity = Rx<EntityModel?>(null);
  
  // 亮度值（0-255）
  final RxInt brightness = 0.obs;
  
  // 色温值
  final RxInt colorTemp = 0.obs;
  
  // RGB颜色
  final Rx<Color> color = Colors.white.obs;
  
  // 风扇速度（百分比）
  final RxInt fanSpeed = 0.obs;
  
  // 温度值
  final RxDouble temperature = 0.0.obs;
  
  // 湿度值
  final RxInt humidity = 0.obs;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 错误信息
  final Rx<String?> errorMessage = Rx<String?>(null);
  
  DeviceDetailController({required this.entityId});
  
  @override
  void onInit() {
    super.onInit();
    
    // 加载实体
    loadEntity();
    
    // 监听实体列表变化
    ever(_haService.entities, (_) {
      loadEntity();
    });
  }
  
  /// 加载实体
  void loadEntity() {
    final foundEntity = _haService.getEntity(entityId);
    
    if (foundEntity != null) {
      entity.value = foundEntity;
      
      // 初始化控制值
      _initControlValues();
    }
  }
  
  /// 初始化控制值
  void _initControlValues() {
    final currentEntity = entity.value;
    if (currentEntity == null) return;
    
    // 初始化亮度
    if (currentEntity.supportsBrightness) {
      brightness.value = currentEntity.attributes['brightness'] as int? ?? 0;
    }
    
    // 初始化色温
    if (currentEntity.supportsColorTemp) {
      colorTemp.value = currentEntity.attributes['color_temp'] as int? ?? 0;
    }
    
    // 初始化RGB颜色
    if (currentEntity.supportsColor) {
      final rgbColor = currentEntity.attributes['rgb_color'] as List<dynamic>?;
      if (rgbColor != null && rgbColor.length >= 3) {
        color.value = Color.fromRGBO(
          rgbColor[0] as int,
          rgbColor[1] as int,
          rgbColor[2] as int,
          1.0,
        );
      }
    }
    
    // 初始化风扇速度
    if (currentEntity.supportsFanSpeed) {
      fanSpeed.value = currentEntity.attributes['percentage'] as int? ?? 0;
    }
    
    // 初始化温度
    if (currentEntity.supportsTemperature) {
      temperature.value = (currentEntity.attributes['temperature'] as num?)?.toDouble() ?? 0.0;
    }
    
    // 初始化湿度
    if (currentEntity.supportsHumidity) {
      humidity.value = currentEntity.attributes['humidity'] as int? ?? 0;
    }
  }
  
  /// 切换实体状态
  Future<void> toggleEntity() async {
    if (entity.value == null) return;
    
    isLoading.value = true;
    errorMessage.value = null;
    
    try {
      if (entity.value!.isOn) {
        await _haService.turnOff(entityId);
      } else {
        await _haService.turnOn(entityId);
      }
    } catch (e) {
      errorMessage.value = '操作失败: $e';
      Get.snackbar(
        '操作失败',
        '无法切换设备状态: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 设置亮度
  Future<void> setBrightness(int value) async {
    if (entity.value == null || !entity.value!.supportsBrightness) return;
    
    brightness.value = value;
    
    try {
      await _haService.turnOn(entityId, data: {'brightness': value});
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置亮度: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 设置色温
  Future<void> setColorTemp(int value) async {
    if (entity.value == null || !entity.value!.supportsColorTemp) return;
    
    colorTemp.value = value;
    
    try {
      await _haService.turnOn(entityId, data: {'color_temp': value});
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置色温: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 设置RGB颜色
  Future<void> setColor(Color value) async {
    if (entity.value == null || !entity.value!.supportsColor) return;
    
    color.value = value;
    
    try {
      await _haService.turnOn(entityId, data: {
        'rgb_color': [value.red, value.green, value.blue]
      });
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置颜色: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 设置风扇速度
  Future<void> setFanSpeed(int value) async {
    if (entity.value == null || !entity.value!.supportsFanSpeed) return;
    
    fanSpeed.value = value;
    
    try {
      await _haService.turnOn(entityId, data: {'percentage': value});
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置风扇速度: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 设置温度
  Future<void> setTemperature(double value) async {
    if (entity.value == null || !entity.value!.supportsTemperature) return;
    
    temperature.value = value;
    
    try {
      await _haService.callService('climate', 'set_temperature', 
        entityId: entityId, 
        serviceData: {'temperature': value}
      );
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置温度: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 设置湿度
  Future<void> setHumidity(int value) async {
    if (entity.value == null || !entity.value!.supportsHumidity) return;
    
    humidity.value = value;
    
    try {
      await _haService.callService('humidifier', 'set_humidity', 
        entityId: entityId, 
        serviceData: {'humidity': value}
      );
    } catch (e) {
      Get.snackbar(
        '操作失败',
        '无法设置湿度: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
