import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/card_editor_controller.dart';
import '../controllers/home_controller.dart';
import '../../../app/services/ha_service.dart';
import '../widgets/cards/base_card.dart';
import '../models/weather_forecast_model.dart';

/// 卡片编辑视图
class CardEditorView extends GetView<CardEditorController> {
  const CardEditorView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('编辑首页卡片'),
        actions: [
          // 保存按钮
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              // 刷新卡片数据
              controller.loadCards();
              // 退出编辑模式
              final homeController = Get.find<HomeController>();
              homeController.isEditMode.value = false;
              Get.back();
              Get.snackbar(
                '保存成功',
                '首页卡片布局已更新',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            tooltip: '保存',
          ),
        ],
      ),
      body: Column(
        children: [
          // 卡片样式选择器
          _buildStyleSelector(context),

          // 卡片列表
          Expanded(
            child: _buildCardList(context),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCardDialog(context),
        tooltip: '添加卡片',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// 构建样式选择器
  Widget _buildStyleSelector(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '卡片样式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // 样式选择器
          Obx(() => SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildStyleChip(
                  context,
                  '默认',
                  CardStyle.defaultStyle,
                ),
                _buildStyleChip(
                  context,
                  '拟物',
                  CardStyle.neumorphic,
                ),
                _buildStyleChip(
                  context,
                  '毛玻璃',
                  CardStyle.glassmorphism,
                ),
                _buildStyleChip(
                  context,
                  '极简',
                  CardStyle.minimal,
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// 构建样式选择芯片
  Widget _buildStyleChip(
    BuildContext context,
    String label,
    CardStyle style,
  ) {
    final isSelected = controller.selectedCardStyle.value == style;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            controller.setCardStyle(style);
          }
        },
        backgroundColor: Theme.of(context).colorScheme.surface,
        selectedColor: Theme.of(context).colorScheme.primary,
        labelStyle: TextStyle(
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  /// 构建卡片列表
  Widget _buildCardList(BuildContext context) {
    return Obx(() {
      if (controller.cards.isEmpty) {
        return const Center(
          child: Text('没有卡片，点击右下角按钮添加'),
        );
      }

      return ReorderableListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.cards.length,
        onReorder: (oldIndex, newIndex) {
          // 处理拖动排序
          if (oldIndex < newIndex) {
            newIndex -= 1;
          }

          final card = controller.cards.removeAt(oldIndex);
          controller.cards.insert(newIndex, card);

          // 更新位置
          for (var i = 0; i < controller.cards.length; i++) {
            controller.updateCardPosition(controller.cards[i].id, i);
          }
        },
        itemBuilder: (context, index) {
          final card = controller.cards[index];

          return Card(
            key: Key(card.id),
            margin: const EdgeInsets.only(bottom: 16),
            child: ListTile(
              leading: _getCardTypeIcon(card.type),
              title: Text(_getCardTitle(card)),
              subtitle: Text(
                card.entityId != null
                    ? '实体ID: ${card.entityId}'
                    : '无关联实体',
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 编辑按钮
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.blue),
                    onPressed: () => _showEditCardDialog(context, card),
                    tooltip: '编辑',
                  ),

                  // 删除按钮
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _showDeleteConfirmDialog(context, card),
                    tooltip: '删除',
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  /// 显示添加卡片对话框
  void _showAddCardDialog(BuildContext context) {
    // 获取HomeController以访问房间列表
    final homeController = Get.find<HomeController>();

    // 卡片类型
    final selectedType = CardType.light.obs;

    // 实体ID
    final selectedEntityId = Rx<String?>(null);

    // 房间ID
    final selectedRoomId = Rx<String?>(controller.selectedRoomId.value);

    // 天气预报类型（仅天气卡片）
    final selectedForecastType = Rx<WeatherForecastType?>(null);

    // 自定义标题
    final customTitleController = TextEditingController();

    // 是否显示标题栏
    final showTitle = true.obs;

    Get.dialog(
      AlertDialog(
        title: const Text('添加卡片'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 卡片类型选择
              const Text(
                '卡片类型',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => DropdownButtonFormField<CardType>(
                value: selectedType.value,
                items: CardType.values.map((type) {
                  return DropdownMenuItem<CardType>(
                    value: type,
                    child: Text(_getCardTypeName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedType.value = value;

                    // 根据类型重置实体ID
                    selectedEntityId.value = null;
                  }
                },
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
              )),

              const SizedBox(height: 16),

              // 实体ID选择（如果需要）
              Obx(() {
                if (_cardTypeNeedsEntity(selectedType.value)) {
                  final entities = _getEntitiesForCardType(selectedType.value);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '选择设备',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: selectedEntityId.value,
                        items: entities.map((entity) {
                          return DropdownMenuItem<String>(
                            value: entity.entityId,
                            child: Text(entity.friendlyName),
                          );
                        }).toList(),
                        onChanged: (value) {
                          selectedEntityId.value = value;
                        },
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ],
                  );
                }

                return const SizedBox.shrink();
              }),

              const SizedBox(height: 16),

              // 天气预报类型选择（仅天气卡片）
              Obx(() {
                if (selectedType.value == CardType.weather) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '预报类型',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<WeatherForecastType>(
                        value: selectedForecastType.value ?? WeatherForecastType.daily,
                        items: [
                          // 每日预报选项
                          DropdownMenuItem<WeatherForecastType>(
                            value: WeatherForecastType.daily,
                            child: Row(
                              children: [
                                const Icon(Icons.today, size: 16),
                                const SizedBox(width: 8),
                                const Text('每日预报'),
                              ],
                            ),
                          ),
                          // 小时预报选项
                          DropdownMenuItem<WeatherForecastType>(
                            value: WeatherForecastType.hourly,
                            child: Row(
                              children: [
                                const Icon(Icons.access_time, size: 16),
                                const SizedBox(width: 8),
                                const Text('小时预报'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          selectedForecastType.value = value;

                          // 如果选择了预报类型，设置默认实体ID
                          if (value != null && selectedEntityId.value == null) {
                            selectedEntityId.value = 'weather.forecast_wo_de_jia';
                          }
                        },
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),

              // 自定义标题
              const Text(
                '自定义标题',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: customTitleController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '留空则使用默认标题',
                ),
              ),

              const SizedBox(height: 16),

              // 显示/隐藏标题栏
              Row(
                children: [
                  Obx(() => Checkbox(
                    value: showTitle.value,
                    onChanged: (value) {
                      if (value != null) {
                        showTitle.value = value;
                      }
                    },
                  )),
                  const Text(
                    '显示标题栏',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 房间选择
              const Text(
                '所属房间',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() {
                final homeController = Get.find<HomeController>();
                // 如果没有房间，显示提示
                if (homeController.rooms.isEmpty) {
                  return const Text('请先添加房间');
                }

                return DropdownButtonFormField<String?>(
                  value: selectedRoomId.value,
                  items: [
                    // 全部房间选项
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('全部房间'),
                    ),
                    // 房间选项
                    ...homeController.rooms.map((room) {
                      return DropdownMenuItem<String?>(
                        value: room.id,
                        child: Text(room.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    // 允许选择"全部房间"（null值）
                    selectedRoomId.value = value;
                  },
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),

          // 添加按钮
          Obx(() {
            final canAdd = !_cardTypeNeedsEntity(selectedType.value) ||
                          selectedEntityId.value != null;

            return ElevatedButton(
              onPressed: canAdd
                  ? () {
                      // 添加卡片
                      // 为不同类型的天气卡片生成不同的ID前缀
                      String cardId;
                      if (selectedType.value == CardType.weather) {
                        // 根据天气预报类型生成不同的ID前缀
                        if (selectedForecastType.value == WeatherForecastType.daily) {
                          cardId = 'weather_daily_${DateTime.now().millisecondsSinceEpoch}';
                        } else {
                          cardId = 'weather_hourly_${DateTime.now().millisecondsSinceEpoch}';
                        }
                      } else {
                        // 其他类型的卡片使用原来的ID格式
                        cardId = '${selectedType.value.toString().split('.').last}_${DateTime.now().millisecondsSinceEpoch}';
                      }

                      // 获取自定义标题（如果为空则设为null）
                      final customTitle = customTitleController.text.trim().isEmpty
                          ? null
                          : customTitleController.text.trim();

                      controller.addCard(CardModel(
                        id: cardId,
                        type: selectedType.value,
                        entityId: selectedEntityId.value,
                        style: controller.selectedCardStyle.value,
                        size: CardSize.medium, // 默认使用中等尺寸
                        position: controller.cards.length,
                        roomId: selectedRoomId.value,
                        forecastType: selectedType.value == CardType.weather ? selectedForecastType.value : null,
                        customTitle: customTitle,
                        showTitle: showTitle.value,
                      ));

                      Get.back();
                    }
                  : null,
              child: const Text('添加'),
            );
          }),
        ],
      ),
    );
  }

  /// 显示编辑卡片对话框
  void _showEditCardDialog(BuildContext context, CardModel card) {
    // 房间ID
    final selectedRoomId = Rx<String?>(card.roomId);

    // 天气预报类型
    final selectedForecastType = Rx<WeatherForecastType?>(card.forecastType);

    // 自定义标题
    final customTitleController = TextEditingController(text: card.customTitle ?? '');

    // 是否显示标题栏
    final showTitle = card.showTitle.obs;

    Get.dialog(
      AlertDialog(
        title: const Text('编辑卡片'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 卡片类型（只读）
              const Text(
                '卡片类型',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                initialValue: _getCardTypeName(card.type),
                readOnly: true,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 16),

              // 实体ID（只读，如果有）
              if (card.entityId != null) ...[
                const Text(
                  '关联设备',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  initialValue: _getEntityName(card.entityId!),
                  readOnly: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                ),

                const SizedBox(height: 16),
              ],

              // 天气预报类型选择（仅天气卡片）
              if (card.type == CardType.weather) ...[
                const Text(
                  '预报类型',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Obx(() => DropdownButtonFormField<WeatherForecastType>(
                  value: selectedForecastType.value ?? WeatherForecastType.daily,
                  items: [
                    // 每日预报选项
                    DropdownMenuItem<WeatherForecastType>(
                      value: WeatherForecastType.daily,
                      child: Row(
                        children: [
                          const Icon(Icons.today, size: 16),
                          const SizedBox(width: 8),
                          const Text('每日预报'),
                        ],
                      ),
                    ),
                    // 小时预报选项
                    DropdownMenuItem<WeatherForecastType>(
                      value: WeatherForecastType.hourly,
                      child: Row(
                        children: [
                          const Icon(Icons.access_time, size: 16),
                          const SizedBox(width: 8),
                          const Text('小时预报'),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    selectedForecastType.value = value;
                  },
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                )),

                const SizedBox(height: 16),
              ],

              // 自定义标题
              const Text(
                '自定义标题',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: customTitleController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: '留空则使用默认标题',
                ),
              ),

              const SizedBox(height: 16),

              // 显示/隐藏标题栏
              Row(
                children: [
                  Obx(() => Checkbox(
                    value: showTitle.value,
                    onChanged: (value) {
                      if (value != null) {
                        showTitle.value = value;
                      }
                    },
                  )),
                  const Text(
                    '显示标题栏',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 房间选择
              const Text(
                '所属房间',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() {
                final homeController = Get.find<HomeController>();
                // 如果没有房间，显示提示
                if (homeController.rooms.isEmpty) {
                  return const Text('请先添加房间');
                }

                return DropdownButtonFormField<String?>(
                  value: selectedRoomId.value,
                  items: [
                    // 全部房间选项
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('全部房间'),
                    ),
                    // 房间选项
                    ...homeController.rooms.map((room) {
                      return DropdownMenuItem<String?>(
                        value: room.id,
                        child: Text(room.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    // 允许选择"全部房间"（null值）
                    selectedRoomId.value = value;
                  },
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),

          // 保存按钮
          ElevatedButton(
            onPressed: () {
              // 获取自定义标题（如果为空则设为null）
              final customTitle = customTitleController.text.trim().isEmpty
                  ? null
                  : customTitleController.text.trim();

              // 更新卡片
              controller.updateCard(CardModel(
                id: card.id,
                type: card.type,
                entityId: card.entityId,
                style: card.style,
                size: card.size,
                position: card.position,
                roomId: selectedRoomId.value,
                forecastType: card.type == CardType.weather ? selectedForecastType.value : null,
                customTitle: customTitle,
                showTitle: showTitle.value,
              ));

              Get.back();
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, CardModel card) {
    Get.dialog(
      AlertDialog(
        title: const Text('删除卡片'),
        content: Text('确定要删除"${_getCardTitle(card)}"卡片吗？'),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),

          // 删除按钮
          TextButton(
            onPressed: () {
              controller.deleteCard(card.id);
              Get.back();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 获取卡片类型图标
  Icon _getCardTypeIcon(CardType type) {
    switch (type) {
      case CardType.weather:
        return const Icon(Icons.cloud, color: Colors.blue);
      case CardType.light:
        return const Icon(Icons.lightbulb, color: Colors.amber);
      case CardType.climate:
        return const Icon(Icons.thermostat, color: Colors.orange);
      case CardType.camera:
        return const Icon(Icons.videocam, color: Colors.red);
      case CardType.sensor:
        return const Icon(Icons.sensors, color: Colors.purple);
      case CardType.cover:
        return const Icon(Icons.blinds, color: Colors.brown);
      case CardType.lock:
        return const Icon(Icons.lock, color: Colors.grey);
      case CardType.media:
        return const Icon(Icons.music_note, color: Colors.pink);
      case CardType.purifier:
        return const Icon(Icons.air, color: Colors.lightBlue);
      case CardType.vacuum:
        return const Icon(Icons.cleaning_services, color: Colors.teal);
      case CardType.energy:
        return const Icon(Icons.bolt, color: Colors.yellow);
    }
  }

  /// 获取卡片类型名称
  String _getCardTypeName(CardType type) {
    switch (type) {
      case CardType.weather:
        return '天气';
      case CardType.light:
        return '灯光';
      case CardType.climate:
        return '温控';
      case CardType.camera:
        return '摄像头';
      case CardType.sensor:
        return '传感器';
      case CardType.cover:
        return '窗帘';
      case CardType.lock:
        return '门锁';
      case CardType.media:
        return '媒体';
      case CardType.purifier:
        return '净化器';
      case CardType.vacuum:
        return '扫地机';
      case CardType.energy:
        return '能源';
    }
  }

  /// 获取卡片标题（包括天气预报类型）
  String _getCardTitle(CardModel card) {
    // 如果是天气卡片，添加预报类型
    if (card.type == CardType.weather) {
      if (card.forecastType == WeatherForecastType.daily) {
        return '天气（每日预报）';
      } else {
        return '天气（小时预报）';
      }
    }

    // 其他类型的卡片使用原来的名称
    return _getCardTypeName(card.type);
  }

  /// 获取实体名称
  String _getEntityName(String entityId) {
    final haService = Get.find<HaService>();
    final entity = haService.getEntity(entityId);

    if (entity != null) {
      return entity.friendlyName;
    }

    return entityId;
  }

  /// 卡片类型是否需要实体
  bool _cardTypeNeedsEntity(CardType type) {
    switch (type) {
      case CardType.weather:
        return false;
      default:
        return true;
    }
  }

  /// 获取卡片类型对应的实体列表
  List<dynamic> _getEntitiesForCardType(CardType type) {
    final haService = Get.find<HaService>();

    switch (type) {
      case CardType.light:
        return haService.getEntitiesByDomain('light');
      case CardType.climate:
        return haService.getEntitiesByDomain('climate');
      case CardType.camera:
        return haService.getEntitiesByDomain('camera');
      case CardType.sensor:
        return haService.getSensorEntities();
      case CardType.cover:
        return haService.getEntitiesByDomain('cover');
      case CardType.lock:
        return haService.getEntitiesByDomain('lock');
      case CardType.media:
        return haService.getEntitiesByDomain('media_player');
      case CardType.purifier:
        return haService.getEntitiesByDomain('fan').where((e) =>
            e.attributes['device_class'] == 'purifier').toList();
      case CardType.vacuum:
        return haService.getEntitiesByDomain('vacuum');
      case CardType.energy:
        return haService.getSensorEntities().where((e) =>
            e.deviceClass == 'energy' ||
            e.deviceClass == 'power').toList();
      default:
        return [];
    }
  }
}
