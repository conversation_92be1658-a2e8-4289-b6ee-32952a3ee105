import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/scene_editor_controller.dart';
import '../../../app/data/models/entity_model.dart';
import '../../../shared/widgets/loading_indicator.dart';

/// 场景编辑页面
class SceneEditorView extends GetView<SceneEditorController> {
  const SceneEditorView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.isEditMode.value ? '编辑场景' : '创建场景')),
        actions: [
          // 保存按钮
          Obx(() => controller.isSaving.value
              ? Container(
                  margin: const EdgeInsets.all(8),
                  width: 40,
                  child: const Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.save),
                  onPressed: () async {
                    final success = await controller.saveScene();
                    if (success) {
                      // 返回到场景列表页面
                      Get.offAllNamed('/scenes');
                    }
                  },
                  tooltip: '保存场景',
                )),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: LoadingIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 错误信息
              if (controller.errorMessage.value != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.errorMessage.value!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),

              // 基本信息卡片
              Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '基本信息',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 场景名称
                      TextField(
                        controller: TextEditingController(text: controller.sceneName.value)
                          ..selection = TextSelection.fromPosition(
                            TextPosition(offset: controller.sceneName.value.length),
                          ),
                        decoration: const InputDecoration(
                          labelText: '场景名称',
                          hintText: '输入场景名称',
                          prefixIcon: Icon(Icons.edit),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: controller.setSceneName,
                      ),
                      const SizedBox(height: 16),

                      // 场景图标和颜色
                      Row(
                        children: [
                          // 图标选择
                          Expanded(
                            child: InkWell(
                              onTap: () => _showIconSelector(context),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 16,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    Obx(() => Icon(
                                      _getIconData(controller.sceneIcon.value ?? 'auto_awesome'),
                                      color: controller.sceneColor.value,
                                    )),
                                    const SizedBox(width: 8),
                                    const Text('选择图标'),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),

                          // 颜色选择
                          Expanded(
                            child: InkWell(
                              onTap: () => _showColorSelector(context),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 16,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  children: [
                                    Obx(() => Container(
                                      width: 24,
                                      height: 24,
                                      decoration: BoxDecoration(
                                        color: controller.sceneColor.value,
                                        shape: BoxShape.circle,
                                      ),
                                    )),
                                    const SizedBox(width: 8),
                                    const Text('选择颜色'),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // 场景分组
                      InkWell(
                        onTap: () => _showGroupSelector(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 16,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.folder),
                              const SizedBox(width: 8),
                              Obx(() {
                                final groupId = controller.sceneGroupId.value;
                                if (groupId == null) {
                                  return const Text('选择分组 (可选)');
                                }

                                // 查找分组名称
                                final group = controller.sceneGroups.firstWhereOrNull(
                                  (g) => g.id == groupId,
                                );

                                return Text(group?.name ?? '未知分组');
                              }),
                              const Spacer(),
                              Obx(() => controller.sceneGroupId.value != null
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () => controller.setSceneGroup(null),
                                      tooltip: '清除分组',
                                      iconSize: 20,
                                    )
                                  : const SizedBox.shrink()),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 设备选择卡片
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '场景设备',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton.icon(
                            icon: const Icon(Icons.add),
                            label: const Text('添加设备'),
                            onPressed: () => _showEntitySelector(context),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // 已选设备列表
                      Obx(() {
                        if (controller.selectedEntities.isEmpty) {
                          return Container(
                            padding: const EdgeInsets.all(16),
                            alignment: Alignment.center,
                            child: const Text(
                              '请添加至少一个设备',
                              style: TextStyle(color: Colors.grey),
                            ),
                          );
                        }

                        return ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: controller.selectedEntities.length,
                          itemBuilder: (context, index) {
                            final entity = controller.selectedEntities[index];
                            return _buildEntityItem(entity);
                          },
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  /// 构建实体项
  Widget _buildEntityItem(EntityModel entity) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          _getEntityIcon(entity),
          color: _getEntityColor(entity),
        ),
        title: Text(entity.friendlyName),
        subtitle: Text(entity.entityId),
        trailing: IconButton(
          icon: const Icon(Icons.delete, color: Colors.red),
          onPressed: () => controller.removeEntity(entity),
          tooltip: '移除设备',
        ),
      ),
    );
  }

  /// 显示图标选择器
  void _showIconSelector(BuildContext context) {
    // 常用图标列表
    final iconList = [
      'auto_awesome',
      'home',
      'lightbulb',
      'tv',
      'bed',
      'movie',
      'music_note',
      'restaurant',
      'local_cafe',
      'wb_sunny',
      'nightlight',
      'celebration',
      'weekend',
      'sports_esports',
      'fitness_center',
      'spa',
      'pool',
      'shower',
      'local_bar',
      'local_dining',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择图标'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: iconList.length,
            itemBuilder: (context, index) {
              final iconName = iconList[index];
              return InkWell(
                onTap: () {
                  controller.setSceneIcon(iconName);
                  Navigator.pop(context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: controller.sceneIcon.value == iconName
                        ? controller.sceneColor.value.shade100
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: controller.sceneIcon.value == iconName
                        ? Border.all(color: controller.sceneColor.value)
                        : null,
                  ),
                  child: Icon(
                    _getIconData(iconName),
                    color: controller.sceneColor.value,
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示颜色选择器
  void _showColorSelector(BuildContext context) {
    // 常用颜色列表 - 使用MaterialColor
    final colorList = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.indigo,
      Colors.blue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择颜色'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: colorList.length,
            itemBuilder: (context, index) {
              final color = colorList[index];
              return InkWell(
                onTap: () {
                  controller.setSceneColor(color);
                  Navigator.pop(context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: controller.sceneColor.value == color
                        ? Border.all(color: Colors.white, width: 2)
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示分组选择器
  void _showGroupSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择分组'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 创建新分组按钮
              ListTile(
                leading: const Icon(Icons.add_circle, color: Colors.green),
                title: const Text('创建新分组'),
                onTap: () {
                  Navigator.pop(context);
                  _showCreateGroupDialog(context);
                },
              ),

              const Divider(),

              // 分组列表
              Obx(() {
                if (controller.sceneGroups.isEmpty) {
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('暂无分组'),
                  );
                }

                return SizedBox(
                  height: 300, // 限制高度
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: controller.sceneGroups.length,
                    itemBuilder: (context, index) {
                      final group = controller.sceneGroups[index];
                      return ListTile(
                        leading: Icon(
                          group.icon != null
                              ? _getIconData(group.icon!)
                              : Icons.folder,
                        ),
                        title: Text(group.name),
                        onTap: () {
                          controller.setSceneGroup(group.id);
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示创建分组对话框
  void _showCreateGroupDialog(BuildContext context) {
    final nameController = TextEditingController();
    String? selectedIcon;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建新分组'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 分组名称
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '分组名称',
                hintText: '输入分组名称',
                prefixIcon: Icon(Icons.folder),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),

            // 分组图标
            InkWell(
              onTap: () {
                // 显示图标选择器
                _showIconSelectorForResult(context).then((iconName) {
                  if (iconName != null && context.mounted) {
                    selectedIcon = iconName;
                    // 刷新对话框
                    Navigator.pop(context);
                    _showCreateGroupDialog(context);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      selectedIcon != null
                          ? _getIconData(selectedIcon!)
                          : Icons.folder,
                    ),
                    const SizedBox(width: 8),
                    const Text('选择图标 (可选)'),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  '错误',
                  '分组名称不能为空',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // 创建分组
              controller.createSceneGroup(
                nameController.text.trim(),
                selectedIcon,
              ).then((success) {
                if (success && context.mounted) {
                  Navigator.pop(context);
                  Get.snackbar(
                    '成功',
                    '分组创建成功',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                  );
                } else if (!success) {
                  Get.snackbar(
                    '错误',
                    '分组创建失败',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              });
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }

  /// 显示图标选择器并返回结果
  Future<String?> _showIconSelectorForResult(BuildContext context) {
    // 常用图标列表
    final iconList = [
      'folder',
      'home',
      'lightbulb',
      'tv',
      'bed',
      'movie',
      'music_note',
      'restaurant',
      'local_cafe',
      'wb_sunny',
      'nightlight',
      'celebration',
      'weekend',
      'sports_esports',
      'fitness_center',
      'spa',
      'pool',
      'shower',
      'local_bar',
      'local_dining',
    ];

    return showDialog<String?>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择图标'),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: iconList.length,
            itemBuilder: (context, index) {
              final iconName = iconList[index];
              return InkWell(
                onTap: () {
                  Navigator.pop(context, iconName);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(_getIconData(iconName)),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示设备选择器
  void _showEntitySelector(BuildContext context) {
    // 搜索控制器
    final searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择设备'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              // 搜索框
              TextField(
                controller: searchController,
                decoration: const InputDecoration(
                  hintText: '搜索设备',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: controller.setSearchQuery,
              ),
              const SizedBox(height: 16),

              // 设备列表
              Expanded(
                child: Obx(() {
                  final entities = controller.filteredAvailableEntities;

                  if (entities.isEmpty) {
                    return const Center(
                      child: Text('没有找到匹配的设备'),
                    );
                  }

                  return ListView.builder(
                    itemCount: entities.length,
                    itemBuilder: (context, index) {
                      final entity = entities[index];
                      final isSelected = controller.sceneEntityIds.contains(entity.entityId);

                      return ListTile(
                        leading: Icon(
                          _getEntityIcon(entity),
                          color: _getEntityColor(entity),
                        ),
                        title: Text(entity.friendlyName),
                        subtitle: Text(entity.entityId),
                        trailing: Checkbox(
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              controller.addEntity(entity);
                            } else {
                              controller.removeEntity(entity);
                            }
                          },
                        ),
                        onTap: () {
                          if (isSelected) {
                            controller.removeEntity(entity);
                          } else {
                            controller.addEntity(entity);
                          }
                        },
                      );
                    },
                  );
                }),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.clearSearch();
              Navigator.pop(context);
            },
            child: const Text('完成'),
          ),
        ],
      ),
    );
  }

  /// 获取实体图标
  IconData _getEntityIcon(EntityModel entity) {
    if (entity.icon != null) {
      return _getIconData(entity.icon!);
    }

    // 根据实体类型返回默认图标
    final domain = entity.entityId.split('.').first;
    switch (domain) {
      case 'light':
        return Icons.lightbulb;
      case 'switch':
        return Icons.power_settings_new;
      case 'climate':
        return Icons.thermostat;
      case 'fan':
        return Icons.toys;
      case 'cover':
        return Icons.blinds;
      case 'media_player':
        return Icons.tv;
      case 'camera':
        return Icons.videocam;
      case 'vacuum':
        return Icons.cleaning_services;
      case 'lock':
        return Icons.lock;
      case 'sensor':
        return Icons.sensors;
      case 'binary_sensor':
        return Icons.sensors;
      default:
        return Icons.devices;
    }
  }

  /// 获取实体颜色
  Color _getEntityColor(EntityModel entity) {
    // 根据实体类型返回颜色
    final domain = entity.entityId.split('.').first;
    switch (domain) {
      case 'light':
        return Colors.amber;
      case 'switch':
        return Colors.green;
      case 'climate':
        return Colors.blue;
      case 'fan':
        return Colors.lightBlue;
      case 'cover':
        return Colors.brown;
      case 'media_player':
        return Colors.purple;
      case 'camera':
        return Colors.red;
      case 'vacuum':
        return Colors.teal;
      case 'lock':
        return Colors.indigo;
      case 'sensor':
        return Colors.orange;
      case 'binary_sensor':
        return Colors.deepOrange;
      default:
        return Colors.grey;
    }
  }

  /// 获取图标数据
  IconData _getIconData(String iconName) {
    // 常用图标映射
    final iconMap = {
      'home': Icons.home,
      'lightbulb': Icons.lightbulb,
      'tv': Icons.tv,
      'bed': Icons.bed,
      'movie': Icons.movie,
      'music_note': Icons.music_note,
      'restaurant': Icons.restaurant,
      'local_cafe': Icons.local_cafe,
      'wb_sunny': Icons.wb_sunny,
      'nightlight': Icons.nightlight,
      'auto_awesome': Icons.auto_awesome,
      'celebration': Icons.celebration,
      'weekend': Icons.weekend,
      'sports_esports': Icons.sports_esports,
      'fitness_center': Icons.fitness_center,
      'spa': Icons.spa,
      'pool': Icons.pool,
      'shower': Icons.shower,
      'local_bar': Icons.local_bar,
      'local_dining': Icons.local_dining,
      'folder': Icons.folder,
    };

    return iconMap[iconName] ?? Icons.auto_awesome;
  }
}