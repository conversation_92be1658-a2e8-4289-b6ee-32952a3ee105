import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../app/config/app_routes.dart';
import '../../controllers/home_controller.dart';

/// 顶部状态栏组件
class StatusBar extends GetView<HomeController> {
  const StatusBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 第一行：家庭状态和天气信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 家庭状态
              Obx(() => _buildHomeStatus(context)),

              // 天气信息
              Obx(() => _buildWeatherInfo(context)),
            ],
          ),

          const SizedBox(height: 12),

          // 第二行：房间选择和安防状态
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 房间选择
              Expanded(
                child: Obx(() => _buildRoomSelector(context)),
              ),

              const SizedBox(width: 16),

              // 安防状态
              Obx(() => _buildSecurityStatus(context)),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建家庭状态
  Widget _buildHomeStatus(BuildContext context) {
    final isAtHome = controller.isAtHome;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isAtHome ? Colors.green.withAlpha(51) : Colors.orange.withAlpha(51),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Icon(
                isAtHome ? Icons.home : Icons.home_outlined,
                size: 18,
                color: isAtHome ? Colors.green : Colors.orange,
              ),
              const SizedBox(width: 6),
              Text(
                isAtHome ? '在家' : '离家',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isAtHome ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        ),

        // 点击切换按钮
        IconButton(
          icon: const Icon(Icons.swap_horiz, size: 18),
          onPressed: controller.toggleHomeStatus,
          tooltip: '切换家庭状态',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  /// 构建天气信息
  Widget _buildWeatherInfo(BuildContext context) {
    final weather = controller.weather.value;

    if (weather == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        // 天气图标
        Icon(
          _getWeatherIcon(weather.condition),
          size: 24,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 8),

        // 温度和湿度
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${weather.temperature}°C',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '湿度 ${weather.humidity}%',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建房间选择器
  Widget _buildRoomSelector(BuildContext context) {
    return InkWell(
      onTap: () => _showRoomSelector(context),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(77),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.room,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                controller.selectedRoom.value?.name ?? '全部房间',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                ),
                const SizedBox(width: 4),
                // 房间管理按钮
                InkWell(
                  onTap: () => Get.toNamed(AppRoutes.roomManager),
                  child: const Icon(
                    Icons.settings,
                    size: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建安防状态
  Widget _buildSecurityStatus(BuildContext context) {
    final securityStatus = controller.securityStatus.value;

    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (securityStatus) {
      case SecurityStatus.disarmed:
        statusColor = Colors.grey;
        statusText = '已撤防';
        statusIcon = Icons.security;
        break;
      case SecurityStatus.armed:
        statusColor = Colors.green;
        statusText = '已警戒';
        statusIcon = Icons.security;
        break;
      case SecurityStatus.triggered:
        statusColor = Colors.red;
        statusText = '已触发';
        statusIcon = Icons.warning_amber;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withAlpha(51),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            size: 16,
            color: statusColor,
          ),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示房间选择器
  void _showRoomSelector(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            const Text(
              '选择房间',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 房间列表
            Obx(() => ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.rooms.length + 1, // +1 for "All Rooms"
              itemBuilder: (context, index) {
                // 全部房间选项
                if (index == 0) {
                  return _buildRoomItem(
                    context,
                    null,
                    'all_rooms',
                    '全部房间',
                    Icons.home,
                  );
                }

                // 房间选项
                final room = controller.rooms[index - 1];
                return _buildRoomItem(
                  context,
                  room,
                  room.id,
                  room.name,
                  _getRoomIcon(room.type),
                );
              },
            )),
          ],
        ),
      ),
    );
  }

  /// 构建房间项
  Widget _buildRoomItem(
    BuildContext context,
    RoomModel? room,
    String id,
    String name,
    IconData icon,
  ) {
    final isSelected = room == controller.selectedRoom.value ||
                      (room == null && controller.selectedRoom.value == null);

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).colorScheme.primary : null,
      ),
      title: Text(
        name,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? Theme.of(context).colorScheme.primary : null,
        ),
      ),
      trailing: isSelected ? const Icon(Icons.check) : null,
      onTap: () {
        controller.selectRoom(room);
        Get.back();
      },
    );
  }

  /// 获取天气图标
  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return Icons.wb_sunny;
      case 'cloudy':
      case 'partly_cloudy':
        return Icons.cloud;
      case 'rainy':
        return Icons.water_drop;
      case 'snowy':
        return Icons.ac_unit;
      case 'stormy':
        return Icons.thunderstorm;
      default:
        return Icons.wb_sunny;
    }
  }

  /// 获取房间图标
  IconData _getRoomIcon(String type) {
    switch (type.toLowerCase()) {
      case 'living_room':
        return Icons.weekend;
      case 'bedroom':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'bathroom':
        return Icons.bathtub;
      case 'office':
        return Icons.computer;
      case 'dining_room':
        return Icons.dining;
      default:
        return Icons.home;
    }
  }
}
