import 'package:flutter/material.dart';

/// 天气预报类型
enum WeatherForecastType {
  /// 每日预报
  daily,

  /// 小时预报
  hourly,
}

/// 天气预报项模型
class WeatherForecastItemModel {
  /// 天气状况
  final String condition;

  /// 日期时间
  final DateTime dateTime;

  /// 风向
  final double? windBearing;

  /// 紫外线指数
  final double? uvIndex;

  /// 温度
  final double temperature;

  /// 最低温度（仅每日预报）
  final double? tempLow;

  /// 风速
  final double windSpeed;

  /// 降水量
  final double precipitation;

  /// 湿度
  final int humidity;

  /// 云覆盖率（仅小时预报）
  final double? cloudCoverage;

  WeatherForecastItemModel({
    required this.condition,
    required this.dateTime,
    this.windBearing,
    this.uvIndex,
    required this.temperature,
    this.tempLow,
    required this.windSpeed,
    required this.precipitation,
    required this.humidity,
    this.cloudCoverage,
  });

  /// 从JSON创建
  factory WeatherForecastItemModel.fromJson(Map<String, dynamic> json) {
    return WeatherForecastItemModel(
      condition: json['condition'] as String,
      dateTime: DateTime.parse(json['datetime'] as String),
      windBearing: json['wind_bearing'] != null ? (json['wind_bearing'] as num).toDouble() : null,
      uvIndex: json['uv_index'] != null ? (json['uv_index'] as num).toDouble() : null,
      temperature: (json['temperature'] as num).toDouble(),
      tempLow: json['templow'] != null ? (json['templow'] as num).toDouble() : null,
      windSpeed: (json['wind_speed'] as num).toDouble(),
      precipitation: (json['precipitation'] as num).toDouble(),
      humidity: (json['humidity'] as num).toInt(),
      cloudCoverage: json['cloud_coverage'] != null ? (json['cloud_coverage'] as num).toDouble() : null,
    );
  }
}

/// 天气预报模型
class WeatherForecastModel {
  /// 预报类型
  final WeatherForecastType type;

  /// 预报项列表
  final List<WeatherForecastItemModel> forecast;

  /// 最后更新时间
  final DateTime lastUpdated;

  WeatherForecastModel({
    required this.type,
    required this.forecast,
    required this.lastUpdated,
  });

  /// 从WebSocket事件创建
  factory WeatherForecastModel.fromEvent(Map<String, dynamic> event) {
    final eventData = event['event'] as Map<String, dynamic>;
    final typeStr = eventData['type'] as String;
    final forecastList = (eventData['forecast'] as List<dynamic>)
        .map((item) => WeatherForecastItemModel.fromJson(item as Map<String, dynamic>))
        .toList();

    return WeatherForecastModel(
      type: typeStr == 'daily' ? WeatherForecastType.daily : WeatherForecastType.hourly,
      forecast: forecastList,
      lastUpdated: DateTime.now(),
    );
  }

  /// 获取预报类型名称
  String getTypeName() {
    switch (type) {
      case WeatherForecastType.daily:
        return '每日';
      case WeatherForecastType.hourly:
        return '小时';
    }
  }

  /// 获取预报类型图标
  IconData getTypeIcon() {
    switch (type) {
      case WeatherForecastType.daily:
        return Icons.calendar_today;
      case WeatherForecastType.hourly:
        return Icons.access_time;
    }
  }
}
