import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';

/// 房间管理视图
class RoomManagerView extends GetView<HomeController> {
  const RoomManagerView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('房间管理'),
        actions: [
          // 添加房间按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddRoomDialog(context),
            tooltip: '添加房间',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.rooms.isEmpty) {
          return const Center(
            child: Text('没有房间，点击右上角按钮添加'),
          );
        }
        
        return ReorderableListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.rooms.length,
          onReorder: (oldIndex, newIndex) {
            // 处理拖动排序
            if (oldIndex < newIndex) {
              newIndex -= 1;
            }
            
            final List<RoomModel> newOrder = List.from(controller.rooms);
            final item = newOrder.removeAt(oldIndex);
            newOrder.insert(newIndex, item);
            
            // 更新顺序
            controller.updateRoomOrder(newOrder);
          },
          itemBuilder: (context, index) {
            final room = controller.rooms[index];
            
            return Card(
              key: Key(room.id),
              margin: const EdgeInsets.only(bottom: 16),
              child: ListTile(
                leading: Icon(
                  _getRoomIcon(room.type),
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: Text(room.name),
                subtitle: Text('类型: ${_getRoomTypeName(room.type)}'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 编辑按钮
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _showEditRoomDialog(context, room),
                      tooltip: '编辑',
                    ),
                    
                    // 删除按钮
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _showDeleteConfirmDialog(context, room),
                      tooltip: '删除',
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }
  
  /// 显示添加房间对话框
  void _showAddRoomDialog(BuildContext context) {
    // 房间名称
    final nameController = TextEditingController();
    
    // 房间类型
    final selectedType = 'living_room'.obs;
    
    Get.dialog(
      AlertDialog(
        title: const Text('添加房间'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 房间名称
              const Text(
                '房间名称',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  hintText: '输入房间名称',
                  border: OutlineInputBorder(),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 房间类型
              const Text(
                '房间类型',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => DropdownButtonFormField<String>(
                value: selectedType.value,
                items: _getRoomTypes().map((type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(_getRoomTypeName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedType.value = value;
                  }
                },
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
              )),
            ],
          ),
        ),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          
          // 添加按钮
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              
              if (name.isEmpty) {
                Get.snackbar(
                  '错误',
                  '请输入房间名称',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }
              
              // 添加房间
              controller.addRoom(RoomModel(
                id: controller.generateRoomId(selectedType.value),
                name: name,
                type: selectedType.value,
                icon: _getRoomIconName(selectedType.value),
                entityIds: [],
              ));
              
              Get.back();
              
              Get.snackbar(
                '成功',
                '已添加房间: $name',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }
  
  /// 显示编辑房间对话框
  void _showEditRoomDialog(BuildContext context, RoomModel room) {
    // 房间名称
    final nameController = TextEditingController(text: room.name);
    
    // 房间类型
    final selectedType = room.type.obs;
    
    Get.dialog(
      AlertDialog(
        title: const Text('编辑房间'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 房间名称
              const Text(
                '房间名称',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  hintText: '输入房间名称',
                  border: OutlineInputBorder(),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 房间类型
              const Text(
                '房间类型',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => DropdownButtonFormField<String>(
                value: selectedType.value,
                items: _getRoomTypes().map((type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(_getRoomTypeName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    selectedType.value = value;
                  }
                },
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
              )),
            ],
          ),
        ),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          
          // 保存按钮
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              
              if (name.isEmpty) {
                Get.snackbar(
                  '错误',
                  '请输入房间名称',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }
              
              // 更新房间
              controller.updateRoom(RoomModel(
                id: room.id,
                name: name,
                type: selectedType.value,
                icon: _getRoomIconName(selectedType.value),
                entityIds: room.entityIds,
              ));
              
              Get.back();
              
              Get.snackbar(
                '成功',
                '已更新房间: $name',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }
  
  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, RoomModel room) {
    Get.dialog(
      AlertDialog(
        title: const Text('删除房间'),
        content: Text('确定要删除"${room.name}"房间吗？'),
        actions: [
          // 取消按钮
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          
          // 删除按钮
          TextButton(
            onPressed: () {
              controller.deleteRoom(room.id);
              Get.back();
              
              Get.snackbar(
                '成功',
                '已删除房间: ${room.name}',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
  
  /// 获取房间类型列表
  List<String> _getRoomTypes() {
    return [
      'living_room',
      'bedroom',
      'kitchen',
      'bathroom',
      'office',
      'dining_room',
      'laundry_room',
      'garage',
      'balcony',
      'other',
    ];
  }
  
  /// 获取房间类型名称
  String _getRoomTypeName(String type) {
    switch (type) {
      case 'living_room':
        return '客厅';
      case 'bedroom':
        return '卧室';
      case 'kitchen':
        return '厨房';
      case 'bathroom':
        return '浴室';
      case 'office':
        return '书房';
      case 'dining_room':
        return '餐厅';
      case 'laundry_room':
        return '洗衣房';
      case 'garage':
        return '车库';
      case 'balcony':
        return '阳台';
      case 'other':
        return '其他';
      default:
        return type;
    }
  }
  
  /// 获取房间图标
  IconData _getRoomIcon(String type) {
    switch (type) {
      case 'living_room':
        return Icons.weekend;
      case 'bedroom':
        return Icons.bed;
      case 'kitchen':
        return Icons.kitchen;
      case 'bathroom':
        return Icons.bathtub;
      case 'office':
        return Icons.computer;
      case 'dining_room':
        return Icons.dining;
      case 'laundry_room':
        return Icons.local_laundry_service;
      case 'garage':
        return Icons.garage;
      case 'balcony':
        return Icons.balcony;
      case 'other':
        return Icons.home;
      default:
        return Icons.home;
    }
  }
  
  /// 获取房间图标名称
  String _getRoomIconName(String type) {
    switch (type) {
      case 'living_room':
        return 'weekend';
      case 'bedroom':
        return 'bed';
      case 'kitchen':
        return 'kitchen';
      case 'bathroom':
        return 'bathtub';
      case 'office':
        return 'computer';
      case 'dining_room':
        return 'dining';
      case 'laundry_room':
        return 'local_laundry_service';
      case 'garage':
        return 'garage';
      case 'balcony':
        return 'balcony';
      case 'other':
        return 'home';
      default:
        return 'home';
    }
  }
}
