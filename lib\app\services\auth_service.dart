import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../config/app_constants.dart';
import 'websocket_service.dart';
import 'log_service.dart';

/// 认证服务，负责用户认证和会话管理
class AuthService extends GetxService {
  // 存储实例
  final GetStorage _storage = GetStorage();

  // WebSocket服务
  final WebSocketService _wsService = Get.find<WebSocketService>();

  // 认证状态
  final RxBool isAuthenticated = false.obs;

  // 服务器信息
  final Rx<String?> serverUrl = Rx<String?>(null);
  final Rx<String?> accessToken = Rx<String?>(null);
  final Rx<String?> haVersion = Rx<String?>(null);

  // 认证结果Completer
  Completer<bool>? _authCompleter;

  // 连接测试Completer
  Completer<bool>? _connectionTestCompleter;

  // 连接测试订阅
  StreamSubscription? _connectionTestSubscription;

  // 消息订阅
  StreamSubscription? _subscription;

  /// 初始化认证服务
  Future<AuthService> init() async {
    // 从存储中加载认证信息
    _loadAuthInfo();

    // 订阅WebSocket消息
    _subscription = _wsService.onMessage.listen(_handleAuthMessages);

    return this;
  }

  /// 从存储中加载认证信息
  void _loadAuthInfo() {
    final savedServerUrl = _storage.read<String>(AppConstants.storageServerUrl);
    final savedAccessToken = _storage.read<String>(AppConstants.storageAccessToken);
    final rememberMe = _storage.read<bool>(AppConstants.storageRememberMe) ?? false;

    if (savedServerUrl != null && savedAccessToken != null && rememberMe) {
      serverUrl.value = savedServerUrl;
      accessToken.value = savedAccessToken;
    }
  }

  /// 保存认证信息
  Future<void> _saveAuthInfo(bool rememberMe) async {
    if (rememberMe) {
      await _storage.write(AppConstants.storageServerUrl, serverUrl.value);
      await _storage.write(AppConstants.storageAccessToken, accessToken.value);
    } else {
      await _storage.remove(AppConstants.storageServerUrl);
      await _storage.remove(AppConstants.storageAccessToken);
    }

    await _storage.write(AppConstants.storageRememberMe, rememberMe);
  }

  /// 处理认证相关的WebSocket消息
  void _handleAuthMessages(Map<String, dynamic> message) {
    final type = message['type'] as String?;

    if (type == 'auth_ok') {
      // 认证成功
      isAuthenticated.value = true;
      haVersion.value = message['ha_version'] as String?;

      if (_authCompleter != null && !_authCompleter!.isCompleted) {
        _authCompleter!.complete(true);
      }
    } else if (type == 'auth_invalid') {
      // 认证失败
      isAuthenticated.value = false;

      if (_authCompleter != null && !_authCompleter!.isCompleted) {
        _authCompleter!.complete(false);
      }
    }
  }

  /// 登录
  Future<bool> login(String url, String token, bool rememberMe) async {
    try {
      // 规范化URL
      final normalizedUrl = _normalizeUrl(url);

      // 更新状态
      serverUrl.value = normalizedUrl;
      accessToken.value = token;

      // 创建认证Completer
      _authCompleter = Completer<bool>();

      // 连接WebSocket
      final connected = await _wsService.connect(normalizedUrl, token);
      if (!connected) {
        return false;
      }

      // 等待认证结果
      final result = await _authCompleter!.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          if (kDebugMode) {
            print('认证超时');
          }
          return false;
        },
      );

      // 如果认证成功，保存认证信息
      if (result) {
        await _saveAuthInfo(rememberMe);
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('登录失败: $e');
      }
      return false;
    }
  }

  /// 测试连接
  ///
  /// 连接到服务器并等待鉴权响应，然后立即断开连接
  /// 支持通过调用 cancelConnectionTest() 方法中断连接测试
  Future<bool> testConnection(String url) async {
    try {
      // 规范化URL
      final normalizedUrl = _normalizeUrl(url);

      // 创建一个Completer来等待鉴权结果
      _connectionTestCompleter = Completer<bool>();

      // 创建一个订阅来监听鉴权响应
      _connectionTestSubscription = _wsService.onMessage.listen((message) {
        final type = message['type'] as String?;

        if (type == 'auth_required') {
          // 收到鉴权请求，但我们不需要发送鉴权消息
          // 因为我们只是测试连接，不需要真正登录
          LogService.to.i('收到鉴权请求，测试连接成功');

          // 完成Completer
          if (_connectionTestCompleter != null && !_connectionTestCompleter!.isCompleted) {
            _connectionTestCompleter!.complete(true);
          }

          // 取消订阅
          _connectionTestSubscription?.cancel();
          _connectionTestSubscription = null;

          // 断开连接，设置不重连标志
          _wsService.disconnect(shouldReconnect: false);
        } else if (type == 'auth_ok' || type == 'auth_invalid') {
          // 如果收到鉴权成功或失败的响应，也算连接成功
          LogService.to.i('收到鉴权响应: $type，测试连接成功');

          // 完成Completer
          if (_connectionTestCompleter != null && !_connectionTestCompleter!.isCompleted) {
            _connectionTestCompleter!.complete(true);
          }

          // 取消订阅
          _connectionTestSubscription?.cancel();
          _connectionTestSubscription = null;

          // 断开连接，设置不重连标志
          _wsService.disconnect(shouldReconnect: false);
        }
      }, onError: (e) {
        // 发生错误
        LogService.to.e('测试连接时发生错误', e);

        // 完成Completer
        if (_connectionTestCompleter != null && !_connectionTestCompleter!.isCompleted) {
          _connectionTestCompleter!.complete(false);
        }

        // 取消订阅
        _connectionTestSubscription?.cancel();
        _connectionTestSubscription = null;
      });

      // 连接WebSocket，设置不重连标志
      final connected = await _wsService.connect(normalizedUrl, '', shouldReconnect: false);

      if (!connected) {
        // 连接失败，取消订阅并返回false
        _connectionTestSubscription?.cancel();
        _connectionTestSubscription = null;
        return false;
      }

      // 等待鉴权结果或超时
      final result = await _connectionTestCompleter!.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          LogService.to.w('测试连接超时');

          // 取消订阅
          _connectionTestSubscription?.cancel();
          _connectionTestSubscription = null;

          // 断开连接，设置不重连标志
          _wsService.disconnect(shouldReconnect: false);

          return false;
        },
      );

      return result;
    } catch (e) {
      LogService.to.e('测试连接失败', e);

      // 清理资源
      _connectionTestSubscription?.cancel();
      _connectionTestSubscription = null;

      return false;
    }
  }

  /// 取消正在进行的连接测试
  Future<void> cancelConnectionTest() async {
    LogService.to.i('取消连接测试 - 开始');

    try {
      // 如果有正在进行的连接测试，取消它
      if (_connectionTestCompleter != null && !_connectionTestCompleter!.isCompleted) {
        LogService.to.d('完成未完成的连接测试Completer');
        // 完成Completer，但结果为false
        _connectionTestCompleter!.complete(false);
      }

      // 取消订阅
      if (_connectionTestSubscription != null) {
        LogService.to.d('取消WebSocket消息订阅');
        await _connectionTestSubscription?.cancel();
        _connectionTestSubscription = null;
      }

      // 断开WebSocket连接
      LogService.to.d('断开WebSocket连接');
      await _wsService.disconnect(shouldReconnect: false);

      // 等待一小段时间确保连接完全关闭
      await Future.delayed(const Duration(milliseconds: 500));

      LogService.to.i('连接测试已取消 - WebSocket连接已断开');
    } catch (e) {
      LogService.to.e('取消连接测试时出错', e);
      
      // 出错时尝试强制断开连接
      try {
        await _wsService.disconnect(shouldReconnect: false);
      } catch (e2) {
        LogService.to.e('强制断开连接失败', e2);
      }
      
      // 重新抛出异常，让调用者知道发生了错误
      rethrow;
    }
  }

  /// 登出
  Future<void> logout() async {
    // 断开WebSocket连接，设置不重连标志
    await _wsService.disconnect(shouldReconnect: false);

    // 清除认证状态
    isAuthenticated.value = false;
    haVersion.value = null;

    // 清除存储的认证信息
    await _storage.remove(AppConstants.storageServerUrl);
    await _storage.remove(AppConstants.storageAccessToken);
  }

  /// 规范化URL
  String _normalizeUrl(String url) {
    var normalizedUrl = url.trim();

    // 确保URL以http或https开头
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = 'http://$normalizedUrl';
    }

    // 移除URL末尾的斜杠
    if (normalizedUrl.endsWith('/')) {
      normalizedUrl = normalizedUrl.substring(0, normalizedUrl.length - 1);
    }

    return normalizedUrl;
  }

  @override
  void onClose() {
    _subscription?.cancel();
    _connectionTestSubscription?.cancel();
    super.onClose();
  }
}
