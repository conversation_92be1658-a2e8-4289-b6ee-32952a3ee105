import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'app/config/app_constants.dart';
import 'app/config/app_pages.dart';
import 'app/config/app_theme.dart';
import 'app/controllers/theme_controller.dart';
import 'app/data/local/storage_service.dart';
import 'app/services/websocket_service.dart';
import 'app/services/auth_service.dart';
import 'app/services/ha_service.dart';
import 'app/services/log_service.dart';
import 'app/services/rest_api_service.dart';
import 'app/services/statistics_service.dart';
import 'app/services/webdav_service.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 设置设备方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 初始化本地存储
  await GetStorage.init();

  // 注册服务
  await initServices();

  // 注册全局控制器
  Get.put(ThemeController(), permanent: true);

  runApp(const MyApp());
}

/// 初始化服务
Future<void> initServices() async {
  // 初始化日志服务
  await Get.putAsync<LogService>(() => LogService().init());

  // 初始化存储服务
  await Get.putAsync<StorageService>(() => StorageService().init());

  // 初始化WebSocket服务
  await Get.putAsync<WebSocketService>(() => WebSocketService().init());

  // 初始化认证服务
  await Get.putAsync<AuthService>(() => AuthService().init());

  // 初始化Home Assistant服务
  await Get.putAsync<HaService>(() => HaService().init());

  // 初始化统计服务
  await Get.putAsync<StatisticsService>(() => StatisticsService().init());

  // 初始化WebDAV服务
  await Get.putAsync<WebDavService>(() => WebDavService().init());

  // 初始化REST API服务
  await Get.putAsync<RestApiService>(() => RestApiService().init());

  // 记录初始化完成日志
  LogService.to.i('所有服务初始化完成');
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Obx(() => GetMaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // 主题配置
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeController.themeMode.value,

      // 路由配置
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      defaultTransition: Transition.fade,

      // 禁用过渡动画背景
      transitionDuration: const Duration(milliseconds: 200),

      // 确保页面是不透明的
      popGesture: true,
      defaultGlobalState: false,

      // 背景颜色
      builder: (context, child) {
        // 确保整个应用使用正确的背景颜色
        return Container(
          color: Theme.of(context).scaffoldBackgroundColor,
          child: child,
        );
      },

      // 本地化配置
      locale: const Locale('zh', 'CN'),
      fallbackLocale: const Locale('en', 'US'),
    ));
  }
}
